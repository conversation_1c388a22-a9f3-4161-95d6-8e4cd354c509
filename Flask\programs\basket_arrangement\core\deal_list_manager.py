"""
Deal List Manager - Copy đầy đủ từ basket_package.py
Đả<PERSON> bảo 100% tính toàn vẹn logic quản lý dữ liệu Deal list
"""

import re
from .constants import (
    DEAL_LIST_HEADER_ROW, 
    DEAL_LIST_DATA_START_ROW,
    DEFAULT_DEAL_LIST_MAPPING,
    VALIDATION_CONFIG,
    col_to_index,
    normalize_timeline
)

class DealListManager:
    """
    Quản lý dữ liệu từ Deal list sheet
    Copy đầy đủ logic từ basket_package.py để đảm bảo tính toàn vẹn 100%
    """
    
    def __init__(self, log_function=None):
        self.log = log_function or print
        
        # Dữ liệu chính
        self.id_to_cluster = {}
        self.id_to_nmv = {}
        self.id_to_timeline = {}
        self.id_to_normalized_timeline = {}
        self.id_to_shop = {}
        self.id_to_review_m = {}
        self.id_to_review_n = {}
        self.id_to_no = {}
        
        # Tập hợp dữ liệu
        self.clusters = set()
        self.shop_ids = set()
        
        # Dữ liệu gốc để tham chiếu
        self.original_timeline_values = {}
        self.original_no_values = {}
        
        # Thông tin timeline
        self.timeline_rows = {}
        self.timeline_row_details = {}
        
        # Mapping cột mặc định
        self.column_mapping = DEFAULT_DEAL_LIST_MAPPING.copy()

    def set_log_function(self, log_function):
        """Thiết lập hàm log"""
        self.log = log_function

    def load_data(self, worksheet, log_function=None):
        """Load dữ liệu từ sheet Deal list với vị trí cột mặc định"""
        if log_function:
            self.set_log_function(log_function)

        return self.load_data_with_column_positions(worksheet, DEFAULT_DEAL_LIST_MAPPING)

    def load_data_with_column_positions(self, worksheet, column_mapping, log_function=None):
        """Load dữ liệu từ sheet Deal list với vị trí cột đã chỉ định"""
        if log_function:
            self.set_log_function(log_function)

        try:
            # Lấy tất cả dữ liệu từ sheet
            all_values = worksheet.get_all_values()
            self.log(f"Đã lấy {len(all_values)} dòng dữ liệu từ Deal List")

            # Kiểm tra sheet có đủ dữ liệu
            if len(all_values) < DEAL_LIST_DATA_START_ROW:  # Cần ít nhất đủ dòng dữ liệu
                return False, "Sheet không có đủ dữ liệu"

            # Lưu mapping cột
            self.column_mapping = column_mapping.copy()

            # Chuyển đổi tên cột thành index
            column_indices = {}
            for key, col_letter in column_mapping.items():
                try:
                    column_indices[key] = col_to_index(col_letter)
                except:
                    self.log(f"Cảnh báo: Không thể chuyển đổi cột {col_letter} cho {key}")
                    column_indices[key] = 0  # Mặc định cột A

            # Reset dữ liệu
            self.id_to_cluster = {}
            self.id_to_nmv = {}
            self.id_to_timeline = {}
            self.id_to_normalized_timeline = {}
            self.id_to_shop = {}
            self.id_to_review_m = {}
            self.id_to_review_n = {}
            self.id_to_no = {}
            self.clusters = set()
            self.shop_ids = set()
            self.original_timeline_values = {}
            self.original_no_values = {}
            self.timeline_rows = {}
            self.timeline_row_details = {}

            # Parse dữ liệu từ dòng bắt đầu đã được chỉ định
            data_start_index = DEAL_LIST_DATA_START_ROW - 1  # Chuyển từ 1-indexed sang 0-indexed

            # Thêm biến đếm timeline và review
            timeline_counts = {}  # {timeline: count}
            timeline_review_counts = {}  # {timeline: review_count}

            for row_index in range(data_start_index, len(all_values)):
                row = all_values[row_index]
                
                # Lấy dữ liệu từ các cột đã chỉ định
                item_id = self._get_cell_value(row, column_indices.get("id_column", 0))
                cluster = self._get_cell_value(row, column_indices.get("cluster_column", 0))
                nmv_str = self._get_cell_value(row, column_indices.get("nmv_column", 0))
                timeline = self._get_cell_value(row, column_indices.get("timeline_column", 0))
                shop_id = self._get_cell_value(row, column_indices.get("shop_id_column", 0))
                review_m = self._get_cell_value(row, column_indices.get("review_m_column", 0))
                review_n = self._get_cell_value(row, column_indices.get("review_n_column", 0))
                no_str = self._get_cell_value(row, column_indices.get("no_column", 0))

                # Bỏ qua dòng không có ID
                if not item_id or item_id.strip() == "":
                    continue

                item_id = item_id.strip()

                # Xử lý NMV
                try:
                    nmv = float(nmv_str.replace(',', '')) if nmv_str else VALIDATION_CONFIG['DEFAULT_GMV_VALUE']
                except ValueError:
                    nmv = VALIDATION_CONFIG['DEFAULT_GMV_VALUE']

                # Xử lý NO
                try:
                    no = int(no_str) if no_str else VALIDATION_CONFIG['DEFAULT_NO_VALUE']
                except ValueError:
                    no = VALIDATION_CONFIG['DEFAULT_NO_VALUE']

                # Lưu dữ liệu
                self.id_to_cluster[item_id] = cluster
                self.id_to_nmv[item_id] = nmv
                self.id_to_timeline[item_id] = timeline
                self.id_to_normalized_timeline[item_id] = normalize_timeline(timeline)
                self.id_to_shop[item_id] = shop_id
                self.id_to_review_m[item_id] = review_m
                self.id_to_review_n[item_id] = review_n
                self.id_to_no[item_id] = no

                # Lưu giá trị gốc
                self.original_timeline_values[item_id] = timeline
                self.original_no_values[item_id] = no_str

                # Thêm vào tập hợp
                if cluster:
                    self.clusters.add(cluster)
                if shop_id:
                    self.shop_ids.add(shop_id)

                # Đếm timeline
                if timeline:
                    normalized = normalize_timeline(timeline)
                    if normalized not in timeline_counts:
                        timeline_counts[normalized] = 0
                        timeline_review_counts[normalized] = 0
                    timeline_counts[normalized] += 1
                    
                    # Đếm review
                    if self.has_review(item_id):
                        timeline_review_counts[normalized] += 1

                # Lưu thông tin dòng cho timeline
                if timeline:
                    normalized = normalize_timeline(timeline)
                    if normalized not in self.timeline_rows:
                        self.timeline_rows[normalized] = []
                    self.timeline_rows[normalized].append(row_index + 1)  # 1-indexed

                    if normalized not in self.timeline_row_details:
                        self.timeline_row_details[normalized] = []
                    self.timeline_row_details[normalized].append({
                        'row': row_index + 1,
                        'id': item_id,
                        'shop': shop_id,
                        'review': self.has_review(item_id),
                        'no': no
                    })

            # Log thống kê
            self.log(f"Đã load {len(self.id_to_cluster)} ID từ Deal List")
            self.log(f"Tìm thấy {len(self.clusters)} cluster")
            self.log(f"Tìm thấy {len(self.shop_ids)} shop")
            self.log(f"Tìm thấy {len(timeline_counts)} timeline khác nhau")

            # Log chi tiết timeline
            for timeline, count in timeline_counts.items():
                review_count = timeline_review_counts.get(timeline, 0)
                original_timeline = self._find_original_timeline(timeline)
                self.log(f"  Timeline '{original_timeline}': {count} ID ({review_count} có review)")

            return True, f"Đã load thành công {len(self.id_to_cluster)} ID"

        except Exception as e:
            error_msg = f"Lỗi khi load dữ liệu Deal List: {str(e)}"
            self.log(error_msg)
            return False, error_msg

    def _get_cell_value(self, row, col_index):
        """Lấy giá trị cell từ dòng và index cột"""
        if col_index < len(row):
            return row[col_index]
        return ""

    def _find_original_timeline(self, normalized_timeline):
        """Tìm timeline gốc từ normalized timeline"""
        for item_id, original in self.original_timeline_values.items():
            if normalize_timeline(original) == normalized_timeline:
                return original
        return normalized_timeline

    def has_review(self, item_id):
        """Kiểm tra ID có review không (cột M hoặc N có dữ liệu)"""
        review_m = self.id_to_review_m.get(item_id, "")
        review_n = self.id_to_review_n.get(item_id, "")
        return bool(review_m and review_m.strip()) or bool(review_n and review_n.strip())

    def get_gmv(self, item_id):
        """Lấy GMV của ID (từ cột NMV)"""
        return self.id_to_nmv.get(item_id, VALIDATION_CONFIG['DEFAULT_GMV_VALUE'])

    def get_no(self, item_id):
        """Lấy NO của ID"""
        return self.id_to_no.get(item_id, VALIDATION_CONFIG['DEFAULT_NO_VALUE'])

    def get_shop(self, item_id):
        """Lấy Shop ID của item"""
        return self.id_to_shop.get(item_id, "")

    def get_cluster(self, item_id):
        """Lấy Cluster của item"""
        return self.id_to_cluster.get(item_id, "")

    def get_timeline(self, item_id):
        """Lấy Timeline gốc của item"""
        return self.id_to_timeline.get(item_id, "")

    def get_normalized_timeline(self, item_id):
        """Lấy Timeline đã chuẩn hóa của item"""
        return self.id_to_normalized_timeline.get(item_id, "")

    def is_id_in_timeline(self, item_id, timeline):
        """Kiểm tra ID có thuộc timeline không"""
        item_timeline = self.get_normalized_timeline(item_id)
        normalized_target = normalize_timeline(timeline)
        return item_timeline == normalized_target

    def get_ids_by_timeline(self, timeline):
        """Lấy danh sách ID thuộc timeline"""
        normalized_target = normalize_timeline(timeline)
        return [item_id for item_id, item_timeline in self.id_to_normalized_timeline.items()
                if item_timeline == normalized_target]

    def get_ids_by_shop(self, shop_id):
        """Lấy danh sách ID thuộc shop"""
        return [item_id for item_id, item_shop in self.id_to_shop.items()
                if item_shop == shop_id]

    def get_ids_by_cluster(self, cluster):
        """Lấy danh sách ID thuộc cluster"""
        return [item_id for item_id, item_cluster in self.id_to_cluster.items()
                if item_cluster == cluster]

    def get_review_ids_by_timeline(self, timeline):
        """Lấy danh sách ID có review thuộc timeline"""
        ids_in_timeline = self.get_ids_by_timeline(timeline)
        return [item_id for item_id in ids_in_timeline if self.has_review(item_id)]

    def get_timeline_statistics(self):
        """Lấy thống kê timeline"""
        stats = {}
        for item_id, timeline in self.id_to_normalized_timeline.items():
            if timeline not in stats:
                stats[timeline] = {
                    'total_ids': 0,
                    'review_ids': 0,
                    'shops': set(),
                    'clusters': set(),
                    'original_timeline': self.get_timeline(item_id)
                }
            
            stats[timeline]['total_ids'] += 1
            if self.has_review(item_id):
                stats[timeline]['review_ids'] += 1
            
            shop = self.get_shop(item_id)
            if shop:
                stats[timeline]['shops'].add(shop)
            
            cluster = self.get_cluster(item_id)
            if cluster:
                stats[timeline]['clusters'].add(cluster)
        
        # Chuyển set thành list để serialize
        for timeline_stats in stats.values():
            timeline_stats['shops'] = list(timeline_stats['shops'])
            timeline_stats['clusters'] = list(timeline_stats['clusters'])
        
        return stats

    def validate_id(self, item_id):
        """Validate ID có tồn tại trong Deal list không"""
        return item_id in self.id_to_cluster

    def get_all_ids(self):
        """Lấy tất cả ID trong Deal list"""
        return list(self.id_to_cluster.keys())

    def get_column_mapping(self):
        """Lấy mapping cột hiện tại"""
        return self.column_mapping.copy()

    def update_column_mapping(self, new_mapping):
        """Cập nhật mapping cột"""
        self.column_mapping.update(new_mapping)
