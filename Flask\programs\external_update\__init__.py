"""
External Update Program
Cập nhật dữ liệu giữa Internal và External Google Sheets
"""

from .routes import external_update_bp

# Metadata cho program
NAME = "🔄 External Update"
DESCRIPTION = "Cập nhật dữ liệu giữa Internal và External Google Sheets với nhiều điều kiện xử lý"
ICON = "fas fa-exchange-alt"

def setup(app):
    """Setup function được gọi khi program được load"""
    # Đăng ký blueprint với URL prefix
    app.register_blueprint(external_update_bp, url_prefix='/external_update')
    print(f"✅ External Update blueprint registered with prefix '/external_update'")
