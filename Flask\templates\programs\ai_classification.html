<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Classification</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        .ai-container {
            width: 100%;
            height: 100vh;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
            border-radius: 0;
            box-sizing: border-box;
            overflow-y: auto;
        }

        .section {
            background: #2d2d2d;
            border: 1px solid #555;
            border-radius: 5px;
            margin: 15px 0;
            padding: 15px;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #fff;
            border-bottom: 1px solid #555;
            padding-bottom: 5px;
        }

        .form-row {
            display: flex;
            align-items: center;
            margin: 10px 0;
            gap: 10px;
        }

        .form-row label {
            min-width: 120px;
            color: #ddd;
        }

        .form-control {
            background: #333;
            border: 1px solid #555;
            color: #fff;
            padding: 8px;
            border-radius: 3px;
            flex: 1;
        }

        .form-control:focus {
            border-color: #777;
            outline: none;
        }

        .btn {
            background: #444;
            border: 1px solid #666;
            color: #fff;
            padding: 8px 16px;
            border-radius: 3px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .btn:hover {
            background: #555;
        }

        .btn-primary {
            background: #0d6efd;
            border-color: #0d6efd;
        }

        .btn-primary:hover {
            background: #0b5ed7;
        }

        .btn-success {
            background: #198754;
            border-color: #198754;
        }

        .btn-success:hover {
            background: #157347;
        }

        .btn-danger {
            background: #dc3545;
            border-color: #dc3545;
        }

        .btn-danger:hover {
            background: #bb2d3b;
        }

        .log-area {
            background: #1a1a1a;
            border: 1px solid #555;
            color: #0f0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            padding: 10px;
            white-space: pre-wrap;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .status-success {
            background: #28a745;
        }

        .status-error {
            background: #dc3545;
        }

        .status-warning {
            background: #ffc107;
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .hidden {
            display: none;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }

        .stat-card {
            background: #3a3a3a;
            border: 1px solid #555;
            border-radius: 5px;
            padding: 15px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #0d6efd;
        }

        .stat-label {
            font-size: 12px;
            color: #ccc;
            margin-top: 5px;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #333;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #0d6efd, #198754);
            width: 0%;
            transition: width 0.3s ease;
        }

        .model-selector {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .model-option {
            background: #333;
            border: 1px solid #555;
            color: #fff;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .model-option.active {
            background: #0d6efd;
            border-color: #0d6efd;
        }

        .model-option:hover {
            background: #555;
        }

        .model-option.active:hover {
            background: #0b5ed7;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .ai-container {
                padding: 10px;
            }

            .form-row {
                flex-direction: column;
                gap: 10px;
                align-items: stretch;
            }

            .form-row > * {
                width: 100%;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="ai-container">
        <h1>🤖 AI Classification</h1>
        <p>Phân loại sản phẩm thông minh bằng AI với OpenAI GPT</p>

        <!-- Google Sheet Information Section -->
        <div class="section">
            <div class="section-title">📊 Google Sheet Information</div>

            <div class="form-row">
                <label>Spreadsheet URL:</label>
                <input type="text" id="sheetUrl" class="form-control"
                       placeholder="Nhập Google Spreadsheet URL">
                <button id="loadSheetBtn" class="btn btn-primary">Load Sheet</button>
            </div>

            <div class="form-row">
                <label>Brand Sheet:</label>
                <select id="brandSheetSelect" class="form-control" disabled>
                    <option value="">Chọn sheet Brand...</option>
                </select>

                <label style="margin-left: 20px;">Deal Sheet:</label>
                <select id="dealSheetSelect" class="form-control" disabled>
                    <option value="">Chọn sheet Deal list...</option>
                </select>

                <button id="mapColumnsBtn" class="btn" disabled>Map cột</button>
            </div>
        </div>

        <!-- AI Configuration Section -->
        <div class="section">
            <div class="section-title">🤖 Cấu hình AI</div>

            <div class="form-row">
                <label>AI Model:</label>
                <div class="model-selector">
                    <div class="model-option active" data-model="gpt-4o-mini">GPT-4o Mini</div>
                    <div class="model-option" data-model="gpt-3.5-turbo">GPT-3.5 Turbo</div>
                    <div class="model-option" data-model="gpt-4">GPT-4</div>
                </div>
            </div>

            <div class="form-row">
                <label>Số dòng xử lý:</label>
                <input type="number" id="lastRow" class="form-control"
                       value="100" min="1" max="10000" style="width: 100px;">

                <button id="processBtn" class="btn btn-success" style="margin-left: auto;">
                    🚀 Bắt đầu phân loại
                </button>
            </div>
        </div>

        <!-- Statistics Section -->
        <div class="section">
            <div class="section-title">📈 Thống kê</div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalProducts">0</div>
                    <div class="stat-label">Tổng sản phẩm</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalBrands">0</div>
                    <div class="stat-label">Tổng thương hiệu</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="apiCalls">0</div>
                    <div class="stat-label">API Calls</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="tokensUsed">0</div>
                    <div class="stat-label">Tokens sử dụng</div>
                </div>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <!-- Log Section -->
        <div class="section">
            <div class="section-title">📝 Log xử lý</div>
            <div id="logArea" class="log-area"></div>
            <div class="form-row" style="margin-top: 10px;">
                <button id="clearLogBtn" class="btn">Xóa log</button>
                <button id="exportResultsBtn" class="btn btn-primary" disabled>Xuất kết quả</button>
            </div>
        </div>
    </div>

    <!-- Column Mapping Modal -->
    <div id="columnMappingModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🗂️ Cấu hình mapping cột</h3>
                <span class="close" onclick="closeColumnMappingModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="mapping-section">
                    <h4>Brand Sheet Mapping</h4>
                    <div class="form-row">
                        <label>Brand Code:</label>
                        <input type="text" id="brandCodeCol" class="form-control" value="A" placeholder="A">
                    </div>
                    <div class="form-row">
                        <label>Brand Type:</label>
                        <input type="text" id="brandTypeCol" class="form-control" value="E" placeholder="E">
                    </div>
                    <div class="form-row">
                        <label>Brand Name:</label>
                        <input type="text" id="brandNameCol" class="form-control" value="D" placeholder="D">
                    </div>
                </div>

                <div class="mapping-section">
                    <h4>Deal Sheet Mapping</h4>
                    <div class="form-row">
                        <label>Deal Brand Code:</label>
                        <input type="text" id="dealBrandCodeCol" class="form-control" value="A" placeholder="A">
                    </div>
                    <div class="form-row">
                        <label>Deal Product:</label>
                        <input type="text" id="dealProductCol" class="form-control" value="H" placeholder="H">
                    </div>
                    <div class="form-row">
                        <label>Deal Price:</label>
                        <input type="text" id="dealPriceCol" class="form-control" value="AB" placeholder="AB">
                    </div>
                    <div class="form-row">
                        <label>Deal Pick:</label>
                        <input type="text" id="dealPickCol" class="form-control" value="N" placeholder="N">
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="saveColumnMapping()">Lưu mapping</button>
                <button class="btn" onclick="closeColumnMappingModal()">Hủy</button>
            </div>
        </div>
    </div>

    <style>
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
        }

        .modal-content {
            background-color: #2d2d2d;
            margin: 2% auto;
            padding: 0;
            border: 1px solid #555;
            border-radius: 5px;
            width: 80%;
            max-width: 600px;
            max-height: 90vh;
            color: #fff;
            overflow-y: auto;
        }

        .modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid #555;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #fff;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid #555;
            text-align: right;
        }

        .modal-footer .btn {
            margin-left: 10px;
        }

        .mapping-section {
            margin-bottom: 20px;
            padding: 15px;
            background: #3a3a3a;
            border-radius: 5px;
        }

        .mapping-section h4 {
            margin: 0 0 15px 0;
            color: #0d6efd;
        }
    </style>

    <script src="{{ url_for('static', filename='js/ai_classification.js') }}"></script>
</body>
</html>
