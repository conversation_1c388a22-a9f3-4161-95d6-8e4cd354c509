"""
AI Classification Logic - Logic chính cho phân loại sản phẩm bằng AI
Copy và clean code từ ai_classification.py gốc
"""

import re
import time
import json
import sqlite3
import logging
from datetime import datetime
from pathlib import Path
from difflib import SequenceMatcher
from openai import OpenAI

# Cấu hình logger
logger = logging.getLogger(__name__)

# Cấu hình OpenAI
OPENAI_API_KEY = "********************************************************************************************************************************************************************"
API_CALL_DELAY = 0.5

# Khởi tạo OpenAI client
openai_client = None
if OPENAI_API_KEY:
    try:
        openai_client = OpenAI(api_key=OPENAI_API_KEY)
    except Exception as e:
        logger.error(f"Failed to initialize OpenAI client: {e}")
        openai_client = None

# Biến toàn cục để theo dõi API usage
api_call_count = 0
total_tokens_used = 0

# Prompt template cho phân loại
PROMPT_TEMPLATE = (
    "Bạn là một chuyên gia phân loại và trích xuất thông tin sản phẩm với khả năng hiểu tiếng Việt chuyên sâu. "
    "Từ thông tin sản phẩm dưới đây, hãy phân tích và trả về các thông tin sau: \n\n"
    "{price_info}"
    "Tên sản phẩm: {product_description}\n\n"
    "Luật trích xuất chi tiết:\n"
    "1. **type_of_product**: loại sản phẩm chính (VD: 'nồi chiên không dầu', 'sữa rửa mặt', 'kem chống nắng'...).\n"
    "   Quy tắc đặc biệt cho type_of_product:\n"
    "   • CHỈ và CHỈ KHI là nước uống/thức uống collagen → 'nước uống collagen'\n"
    "   • camera/camera wifi/camera ip → 'camera'\n"
    "   • tinh chất/serum/essence → 'serum'\n"
    "   • tẩy da chết/scrub/exfoliator → 'tẩy da chết'\n"
    "   • điện thoại/smartphone → 'điện thoại'\n"
    "   • tã/bỉm → 'tã'\n"
    "   • kit trắng răng/trắng răng → 'kit trắng răng'\n"
    "   • túi xách/túi đeo vai → 'túi xách'\n"
    "   • máy lọc không khí/lọc không khí → 'máy lọc không khí'\n"
    "   • CHỈ và CHỈ KHI chứa chính xác từ khóa: áo ngực/quần lót nữ/bra/áo lót → 'đồ lót nữ'\n"
    "   • son/lipstick/son tint/balm/kem môi → 'son môi'\n"
    "   • phấn mắt/mascara/eyeliner/kẻ mắt/má hồng/eyeshadow/phấn → 'makeup'\n"
    "   • mặt nạ/mask/mặt nạ đất sét/mặt nạ giấy/mặt nạ ngủ → 'mặt nạ dưỡng da'\n"
    "   • nước cân bằng/toner/nước hoa hồng → 'toner'\n"
    "2. **brand**: tên thương hiệu nếu có (VD: 'LocknLock', 'Anessa'); nếu không có => chuỗi rỗng.\n"
    "3. **product_name**: phần tên riêng của sản phẩm. (VD 'Máy ép chậm Sunhouse SHD5515', 'Máy lọc không khí Sunhouse SHD-15AP9715')\n"
    "4. **code**: mã sản phẩm (VD 'F9', 'EH015', 'MC210K', 'SHD-15AP9715', ...). Nếu không tìm thấy => chuỗi rỗng.\n"
    "5. **capacity**: dung tích, size, công suất, v.v. (VD '50ml', '250g', 'size 41', '1000W', '10000mAh').\n"
    "6. **quantity**: số lượng sản phẩm trong combo/bộ (mặc định là 1).\n\n"
    "Hãy trả lời theo định dạng sau (KHÔNG trả về dạng JSON):\n"
    "Phân loại: [type_of_product]\n"
    "Brand: [brand]\n"
    "Tên sản phẩm: [product_name]\n"
    "Mã sản phẩm: [code]\n"
    "Dung tích/Kích thước: [capacity]\n"
    "Số lượng: [quantity]"
)

def get_data_directory():
    """Tạo và trả về đường dẫn đến thư mục lưu trữ dữ liệu cho AI Classification"""
    import os
    try:
        app_data_path = os.environ.get('LOCALAPPDATA', '')
        if not app_data_path:
            app_data_path = str(Path.home() / "AppData" / "Local")
            if not os.path.exists(app_data_path):
                documents_path = os.path.join(str(Path.home()), "Documents")
                app_data_path = os.path.join(documents_path, "Data All in One Data")
                os.makedirs(app_data_path, exist_ok=True)

        data_dir = Path(app_data_path) / "Data All in One" / "AI Classification"
        os.makedirs(data_dir, exist_ok=True)

        # Kiểm tra quyền ghi
        test_file = data_dir / "test_write_permission.txt"
        try:
            with open(test_file, 'w') as f:
                f.write("Test write permission")
            os.remove(test_file)
        except Exception as e:
            documents_path = os.path.join(str(Path.home()), "Documents")
            alt_data_dir = Path(documents_path) / "Data All in One" / "AI Classification"
            os.makedirs(alt_data_dir, exist_ok=True)
            return alt_data_dir

        return data_dir
    except Exception as e:
        documents_path = os.path.join(str(Path.home()), "Documents")
        fallback_dir = Path(documents_path) / "Data All in One" / "AI Classification"
        os.makedirs(fallback_dir, exist_ok=True)
        return fallback_dir

# Khởi tạo data directory
DATA_DIR = get_data_directory()
DB_PATH = DATA_DIR / "active_learning.db"

def setup_database():
    """Initialize SQLite database for active learning storage"""
    db_path = DATA_DIR / "active_learning.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    # Create feedback table with product names
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS feedback (
        brand_code TEXT,
        classification TEXT,
        product_names TEXT,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (brand_code)
    )
    ''')

    # Create product classification table for individual product mappings with price
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS product_classifications (
        product_name TEXT,
        classification TEXT,
        brand_code TEXT,
        price INTEGER DEFAULT 0,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (product_name)
    )
    ''')

    # Create similar product classification table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS similar_products (
        product_name TEXT,
        similar_to TEXT,
        classification TEXT,
        similarity_score REAL,
        price INTEGER DEFAULT 0,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (product_name)
    )
    ''')

    # Tạo bảng lưu trữ brands (thương hiệu)
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS brands (
        brand_code TEXT PRIMARY KEY,
        brand_name TEXT,
        classification TEXT,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Tạo bảng lưu trữ thiết lập
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Tạo bảng lưu trữ lịch sử giá với thông tin nguồn và ngày
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS price_history (
        product_name TEXT,
        price INTEGER,
        live_session TEXT,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (product_name, timestamp)
    )
    ''')

    # Tạo bảng sync_status để theo dõi trạng thái đồng bộ lên Google Sheets
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS sync_status (
        brand_code TEXT PRIMARY KEY,
        sync_status INTEGER DEFAULT 0,
        last_sync_time TIMESTAMP,
        last_classification TEXT,
        spreadsheet_id TEXT
    )
    ''')

    # Tạo bảng high_end_brands để lưu trữ thông tin thương hiệu cao cấp
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS high_end_brands (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        brand_name TEXT UNIQUE,
        is_active INTEGER DEFAULT 1,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Tạo bảng feedback_history để lưu trữ lịch sử feedback
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS feedback_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        brand_code TEXT,
        old_classification TEXT,
        new_classification TEXT,
        feedback_type TEXT,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    conn.commit()
    return conn

def post_process(result: str) -> str:
    """Parse the result string to extract proper category name"""
    text = result.strip().lower()

    # Dictionary for common phrases to normalize
    if ("nước uống collagen" in text or
        "thức uống collagen" in text or
        "collagen uống" in text or
        ("nước uống" in text and "collagen" in text) or
        ("thức uống" in text and "collagen" in text)):
        return "nước uống collagen"

    if any(term in text for term in ["camera", "camera wifi", "camera ip"]):
        return "camera"

    if any(term in text for term in ["tinh chất", "serum", "essence"]):
        return "serum"

    if any(term in text for term in ["tẩy da chết", "scrub", "exfoliator"]):
        return "tẩy da chết"

    if any(term in text for term in ["điện thoại", "smartphone"]):
        return "điện thoại"

    if any(term in text for term in ["tã", "bỉm"]):
        return "tã"

    if any(term in text for term in ["kit trắng răng", "trắng răng"]):
        return "kit trắng răng"

    if any(term in text for term in ["túi xách", "túi đeo vai"]):
        return "túi xách"

    if any(term in text for term in ["máy lọc không khí", "lọc không khí"]):
        return "máy lọc không khí"

    # Kiểm tra CHỈ và CHỈ KHI chứa chính xác từ khóa cho "Đồ lót nữ"
    exact_keywords = ["áo ngực", "quần lót nữ", "bra", "áo lót"]
    for keyword in exact_keywords:
        if keyword in text:
            return "đồ lót nữ"

    if any(term in text for term in ["son", "lipstick", "son tint", "balm", "kem môi"]):
        return "son môi"

    if any(term in text for term in ["phấn mắt", "mascara", "eyeliner", "kẻ mắt", "má hồng", "eyeshadow", "phấn"]):
        return "makeup"

    if any(term in text for term in ["mặt nạ", "mask", "mặt nạ đất sét", "mặt nạ giấy", "mặt nạ ngủ"]):
        return "mặt nạ dưỡng da"

    if any(term in text for term in ["nước cân bằng", "toner", "nước hoa hồng"]):
        return "toner"

    # Get first line if multiple lines
    if '\n' in result:
        text = result.split('\n')[0].strip()

    return text.strip().lower()

def string_similarity(str1, str2):
    """Calculate similarity between two strings"""
    return SequenceMatcher(None, str1.lower(), str2.lower()).ratio()

def format_price_vnd(price):
    """Format price in VND"""
    if price == 0:
        return "0"
    return f"{price:,}".replace(",", ".")

# Initialize database if not exists
if not DB_PATH.exists():
    setup_database()

def find_similar_product(product_name, threshold=0.85):
    """
    Finds the most similar product in the database
    Returns tuple (product_name, similarity_score) or None if no match
    """
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()

        # Kiểm tra bảng similar_products có tồn tại không
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='similar_products'")
        if not cursor.fetchone():
            # Tạo bảng nếu chưa tồn tại
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS similar_products (
                product_name TEXT,
                similar_to TEXT,
                classification TEXT,
                similarity_score REAL,
                price INTEGER DEFAULT 0,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (product_name)
            )
            ''')
            conn.commit()
            conn.close()
            return None

        # Lấy tất cả sản phẩm từ database
        cursor.execute("SELECT product_name FROM product_classifications")
        stored_products = [row[0] for row in cursor.fetchall()]

        if not stored_products:
            conn.close()
            return None

        # Tìm sản phẩm giống nhất
        best_similarity = 0
        best_match = None

        for stored_product in stored_products:
            similarity = string_similarity(product_name.lower(), stored_product.lower())
            if similarity > best_similarity and similarity >= threshold:
                best_similarity = similarity
                best_match = stored_product

        conn.close()

        if best_match:
            return (best_match, best_similarity)
        return None
    except Exception as e:
        print(f"Error finding similar product: {e}")
        try:
            conn.close()
        except:
            pass
        return None

def analyze_combo_set(product_name, model="gpt-4o-mini"):
    """
    Phân tích tên sản phẩm combo/bộ để xác định số lượng sản phẩm trong bộ
    Trả về số lượng sản phẩm dự đoán
    """
    global api_call_count, total_tokens_used

    product_lower = product_name.lower()

    # Detect patterns with regex
    number_pattern = r"(?:combo|set|bộ)(?:\s+x|\s+of\s+|\s+|-)(\d+)"
    pieces_pattern = r"(\d+)(?:\s+|-)(?:món|piece|miếng|items|sp|sản phẩm)"
    in_pattern = r"(\d+)(?:\s*|-)in(?:\s*|-)1"

    # Check for common patterns first
    for pattern in [number_pattern, pieces_pattern, in_pattern]:
        matches = re.findall(pattern, product_lower)
        if matches:
            try:
                return int(matches[0])
            except:
                pass

    # Check for "bộ đôi" = 2
    if any(term in product_lower for term in ["bộ đôi", "combo đôi", "set đôi"]):
        return 2

    # Check for "bộ 3" without space
    if any(term in product_lower for term in ["bộ3", "combo3", "set3"]):
        return 3

    # If none of the patterns matched, but it contains combo/set/bộ keywords, use API
    if any(term in product_lower for term in ["combo", "set", "bộ"]) and model and OPENAI_API_KEY:
        prompt = (
            f"Phân tích tên sản phẩm sau và xác định có BAO NHIÊU SẢN PHẨM trong bộ: \"{product_name}\"\n"
            f"Chỉ trả về số lượng dưới dạng số nguyên (ví dụ: 2, 3, 4...).\n"
            f"Nếu không xác định được, trả về 1.\n"
        )

        try:
            openai_client = OpenAI(api_key=OPENAI_API_KEY)
            response = openai_client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=10
            )
            try:
                if hasattr(response, 'usage') and response.usage is not None:
                    total_tokens_used += response.usage.total_tokens
            except Exception:
                pass

            result = response.choices[0].message.content.strip()
            api_call_count += 1
            time.sleep(API_CALL_DELAY)

            # Lấy số từ kết quả
            try:
                count = int(re.search(r"\d+", result).group())
                if 1 <= count <= 10:  # Giới hạn hợp lý
                    return count
            except:
                pass
        except:
            pass

    # Fallback: có combo nhưng không xác định được số lượng
    if any(term in product_lower for term in ["combo", "set", "bộ", "kit"]):
        return 2
    return 1

def extract_product_category(product_info, price=0, prompt_template=PROMPT_TEMPLATE, model="gpt-4o-mini"):
    """
    Phân loại sản phẩm bằng cách gọi API OpenAI để phân tích.

    Returns: tuple (classification_result, prompt, extracted_data)
    - classification_result: kết quả phân loại cuối cùng
    - prompt: nội dung prompt đã sử dụng
    - extracted_data: dữ liệu trích xuất thêm từ sản phẩm (nếu có)
    """
    global api_call_count, total_tokens_used

    # Kiểm tra cơ sở dữ liệu nếu sản phẩm đã được phân loại trước đó
    similar_product = find_similar_product(product_info)
    if similar_product:
        print(f"Using similar product match: '{product_info}' -> '{similar_product[0]}' (Score: {similar_product[1]:.2f})")
        try:
            conn = sqlite3.connect(str(DB_PATH))
            cursor = conn.cursor()

            # Kiểm tra bảng product_classifications có tồn tại không
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='product_classifications'")
            if not cursor.fetchone():
                # Tự động tạo bảng nếu chưa tồn tại
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS product_classifications (
                    product_name TEXT,
                    classification TEXT,
                    brand_code TEXT,
                    price INTEGER DEFAULT 0,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (product_name)
                )
                ''')
                conn.commit()
                conn.close()
                return None, "", None

            # Thực hiện truy vấn
            cursor.execute("SELECT classification, brand_code FROM product_classifications WHERE product_name = ?",
                        (similar_product[0],))
            result = cursor.fetchone()
            conn.close()

            if result:
                return result[0], "", None
        except Exception as e:
            print(f"Error querying database: {e}")
            try:
                conn.close()
            except:
                pass

    # Phân tích số lượng sản phẩm nếu là combo/set
    combo_quantity = 1
    if any(term in product_info.lower() for term in ['bộ', 'combo', 'set']):
        combo_quantity = analyze_combo_set(product_info, model)

    # Xử lý prompt
    product_description = product_info
    price_text = ""
    if price > 0:
        price_text = f"Giá sản phẩm: {format_price_vnd(price)} VND\n"

    # Chuẩn bị prompt
    prompt = prompt_template.format(product_description=product_description, price_info=price_text)

    # Số lần thử lại khi gặp lỗi API
    max_retries = 3
    retry_delay = 2
    retry_count = 0

    if not OPENAI_API_KEY:
        # Nếu không có API key, trả về phân loại mặc định
        return "unknown", prompt, {"error": "No OpenAI API key", "product_info": product_info, "quantity": combo_quantity}

    while retry_count < max_retries:
        try:
            # Gọi API OpenAI
            openai_client = OpenAI(api_key=OPENAI_API_KEY)
            response = openai_client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": "Bạn là một trợ lý AI chuyên phân loại sản phẩm trong lĩnh vực thời trang, mỹ phẩm và hàng tiêu dùng."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=500
            )

            # Tính token đã sử dụng
            api_call_count += 1
            try:
                if hasattr(response, 'usage') and response.usage is not None:
                    total_tokens_used += response.usage.total_tokens
            except Exception as e:
                print(f"Lỗi tính token: {str(e)}")

            # Lấy kết quả
            result_text = response.choices[0].message.content.strip()

            # Xử lý kết quả từ AI
            result_lines = result_text.strip().split('\n')
            classification = None
            extracted_data = {}

            for line in result_lines:
                line = line.strip()
                if not line:
                    continue

                # Tìm dòng bắt đầu bằng "Phân loại:" hoặc "Classification:"
                if line.lower().startswith(("phân loại:", "classification:")):
                    classification = line.split(":", 1)[1].strip()
                    extracted_data["type_of_product"] = classification
                elif line.lower().startswith("brand:"):
                    extracted_data["brand"] = line.split(":", 1)[1].strip()
                elif line.lower().startswith("tên sản phẩm:"):
                    extracted_data["product_name"] = line.split(":", 1)[1].strip()
                elif line.lower().startswith("mã sản phẩm:"):
                    extracted_data["code"] = line.split(":", 1)[1].strip()
                elif line.lower().startswith(("dung tích", "kích thước")):
                    extracted_data["capacity"] = line.split(":", 1)[1].strip()
                elif line.lower().startswith("số lượng:"):
                    try:
                        quantity_str = line.split(":", 1)[1].strip()
                        quantity = int(re.search(r'\d+', quantity_str).group())
                        extracted_data["quantity"] = quantity
                    except:
                        extracted_data["quantity"] = 1

            # Nếu không thể trích xuất phân loại theo định dạng, sử dụng toàn bộ kết quả
            if not classification:
                classification = post_process(result_text)
            else:
                classification = post_process(classification)

            # Thêm thông tin còn thiếu
            extracted_data["product_info"] = product_info
            if "quantity" not in extracted_data:
                extracted_data["quantity"] = combo_quantity

            # Lưu vào DB cho các lần tìm kiếm tương đồng sau này
            if similar_product is None:
                try:
                    conn = sqlite3.connect(str(DB_PATH))
                    cursor = conn.cursor()

                    # Kiểm tra và tạo bảng similar_products nếu chưa tồn tại
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='similar_products'")
                    if not cursor.fetchone():
                        cursor.execute('''
                        CREATE TABLE IF NOT EXISTS similar_products (
                            product_name TEXT,
                            similar_to TEXT,
                            classification TEXT,
                            similarity_score REAL,
                            price INTEGER DEFAULT 0,
                            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            PRIMARY KEY (product_name)
                        )
                        ''')

                    cursor.execute(
                        "INSERT OR REPLACE INTO similar_products (product_name, similar_to, classification, similarity_score, price) VALUES (?, ?, ?, ?, ?)",
                        (product_info, product_info, classification, 1.0, price)
                    )
                    conn.commit()
                    conn.close()
                except Exception as e:
                    print(f"Error saving to similar_products: {e}")
                    try:
                        conn.close()
                    except:
                        pass

            return classification, prompt, extracted_data

        except Exception as e:
            retry_count += 1
            error_msg = str(e)

            print(f"Error processing product: {product_info}")
            print(f"Error: {error_msg}")

            # Kiểm tra lỗi Cloudflare hoặc giới hạn tần suất
            is_rate_limit = "rate limit" in error_msg.lower() or "rate_limit" in error_msg.lower()
            is_cloudflare = "520" in error_msg or "cloudflare" in error_msg.lower()

            if retry_count < max_retries:
                wait_time = retry_delay * (2 ** (retry_count - 1))
                if is_rate_limit:
                    wait_time *= 2

                print(f"Retrying in {wait_time} seconds... (Attempt {retry_count}/{max_retries})")
                time.sleep(wait_time)
            else:
                if is_rate_limit:
                    if model != "gpt-3.5-turbo":
                        print("Rate limit exceeded, trying fallback model...")
                        return extract_product_category(product_info, price, prompt_template, "gpt-3.5-turbo")
                elif is_cloudflare:
                    print("Connection error from Cloudflare. Network issue or OpenAI service unavailable.")
                    return "Unknown", prompt, {"error": "Cloudflare connection error", "product_info": product_info}

                fallback_classification = "Unknown"
                return fallback_classification, prompt, {"error": error_msg, "product_info": product_info}

def parse_spreadsheet_id(url: str) -> str:
    """Parse spreadsheet ID from URL or return the ID if directly provided"""
    # Check if the input is already a spreadsheet ID format
    if re.match(r'^[a-zA-Z0-9-_]+$', url) and len(url) > 20:
        return url

    # Try to extract from standard Google Sheets URL
    match = re.search(r"/d/([a-zA-Z0-9-_]+)", url)
    if (match):
        return match.group(1)

    # Try to extract from alternate URL formats
    match = re.search(r"spreadsheets/d/([a-zA-Z0-9-_]+)", url)
    if match:
        return match.group(1)

    return url  # Return the input as is if no pattern matches

def extract_date_from_sheet_name(sheet_name):
    """Trích xuất thông tin ngày tháng từ tên sheet để lấy thông tin phiên live"""
    # Mẫu kiểm tra các dạng:
    # - "d.m Internal" hoặc "dd.mm Internal"
    # - "[d.m] Internal" hoặc "[dd.mm] Internal"
    patterns = [
        r'(\d{1,2})\.(\d{1,2}).*Internal',    # d.m hoặc dd.mm theo sau là từ Internal
        r'\[(\d{1,2})\.(\d{1,2})\].*Internal'  # [d.m] hoặc [dd.mm] theo sau là từ Internal
    ]

    for pattern in patterns:
        match = re.search(pattern, sheet_name)
        if match:
            # Lấy ngày và tháng dưới dạng số (không cần dẫn 0)
            day = int(match.group(1))
            month = int(match.group(2))
            # Trả về định dạng d.m
            return f"{day}.{month}"

    # Nếu không có mẫu phù hợp, trả về ngày hiện tại dưới dạng d.m
    current_date = datetime.now()
    return f"{current_date.day}.{current_date.month}"

def extract_live_session_from_spreadsheet(spreadsheet):
    """Trích xuất thông tin phiên live từ tên của Spreadsheet

    Args:
        spreadsheet: Đối tượng Google Spreadsheet đã load

    Returns:
        tuple: (phiên live, tên đầy đủ của spreadsheet)
    """
    # Lấy tên đầy đủ của spreadsheet
    full_name = ""
    try:
        # Nếu spreadsheet có thuộc tính title
        if hasattr(spreadsheet, 'title'):
            full_name = spreadsheet.title
        # Hoặc nếu có thuộc tính properties và title
        elif hasattr(spreadsheet, 'properties') and hasattr(spreadsheet.properties, 'title'):
            full_name = spreadsheet.properties.title
        # Thử lấy thông qua dunder method
        elif hasattr(spreadsheet, '_properties') and 'title' in spreadsheet._properties:
            full_name = spreadsheet._properties['title']
        else:
            # Nếu không thể lấy tên từ các thuộc tính chuẩn, thử gọi API
            try:
                # Có thể cần phải gọi API lấy metadata
                full_name = spreadsheet.fetch_sheet_metadata().get('properties', {}).get('title', '')
            except:
                pass
    except Exception as e:
        print(f"Lỗi khi lấy tên spreadsheet: {str(e)}")

    # Kiểm tra xem còn cách nào khác để lấy tên
    if not full_name and hasattr(spreadsheet, 'get_title'):
        try:
            full_name = spreadsheet.get_title()
        except:
            pass

    # Nếu không lấy được tên, trả về ngày hiện tại
    if not full_name:
        current_date = datetime.now()
        return f"{current_date.day}.{current_date.month}", "Unknown Spreadsheet"

    # Trước tiên, tìm phiên từ đầu tên trước "Internal" - ưu tiên cách này
    # Mẫu để tìm các chữ số ở đầu tên sheet trước từ "Internal"
    pattern_internal = r'^([0-9]+(?:\.[0-9]+)?)\s*(?:Internal|Int)'
    match_internal = re.search(pattern_internal, full_name)
    if match_internal:
        return match_internal.group(1), full_name

    # Tìm mẫu dạng số ở đầu chuỗi, ví dụ "3.3" trong "3.3 Internal | SPE x DL Deal List"
    pattern_start = r'^([0-9]+(?:\.[0-9]+)?)'
    match_start = re.search(pattern_start, full_name)
    if match_start:
        return match_start.group(1), full_name

    # Nếu không tìm thấy ở đầu, thử các mẫu khác
    # Các mẫu phổ biến để trích xuất thông tin phiên live từ tên spreadsheet
    live_patterns = [
        r'\[([0-9]+(?:\.[0-9]+)?)\]',                # Tìm dạng [3.3]
        r'Live\s+([0-9]+(?:\.[0-9]+)?)',             # Tìm dạng "Live 3.3"
        r'Phiên\s+([0-9]+(?:\.[0-9]+)?)',            # Tìm dạng "Phiên 3.3"
        r'Live\s+ngày\s+([0-9]+(?:\.[0-9]+)?)',      # Tìm dạng "Live ngày 3.3"
        r'Deal\s+list\s+([0-9]+(?:\.[0-9]+)?)',      # Tìm dạng "Deal list 3.3"
        r'([0-9]+(?:\.[0-9]+)?)\s+Deal',             # Tìm dạng "3.3 Deal"
        r'([0-9]+(?:\.[0-9]+)?)\s+Internal',         # Tìm dạng "3.3 Internal"
    ]

    for pattern in live_patterns:
        match = re.search(pattern, full_name)
        if match:
            return match.group(1), full_name

    # Nếu không tìm thấy thông tin phiên live, trả về giá trị mặc định
    return "Unknown", full_name

def save_product_classification(product_name, classification, brand_code, price=0, sheet_name="", spreadsheet_id="", live_session=None):
    """Save individual product classification to database with price information"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()

        # Bắt đầu transaction
        cursor.execute("BEGIN TRANSACTION")

        # Kiểm tra xem brand_code đã tồn tại trong bảng brands chưa
        cursor.execute("SELECT COUNT(*) FROM brands WHERE brand_code = ?", (brand_code,))
        brand_exists = cursor.fetchone()[0] > 0

        # Nếu brand_code chưa tồn tại, thêm vào bảng brands
        if not brand_exists:
            # Thử trích xuất tên brand từ tên sản phẩm nếu không có thông tin khác
            brand_name = brand_code  # Mặc định sử dụng brand_code làm tên

            cursor.execute(
                "INSERT INTO brands (brand_code, brand_name, classification) VALUES (?, ?, ?)",
                (brand_code, brand_name, classification)
            )
            print(f"Đã tự động thêm brand mới: {brand_code}")

        # Lưu classification
        cursor.execute(
            "INSERT OR REPLACE INTO product_classifications (product_name, classification, brand_code, price) VALUES (?, ?, ?, ?)",
            (product_name, classification, brand_code, price)
        )

        # Lưu lịch sử giá nếu giá > 0
        if price > 0:
            # Sử dụng live_session đã được truyền vào nếu có
            if not live_session:
                # Nếu live_session không được cung cấp, sử dụng ngày hiện tại
                current_date = datetime.now()
                live_session = f"{current_date.day}.{current_date.month}"

            cursor.execute(
                "INSERT INTO price_history (product_name, price, live_session) VALUES (?, ?, ?)",
                (product_name, price, live_session)
            )

        # Cập nhật trạng thái đồng bộ cho brand_code
        try:
            # Kiểm tra xem brand_code đã có trong bảng sync_status chưa
            cursor.execute("SELECT sync_status FROM sync_status WHERE brand_code = ?", (brand_code,))
            result = cursor.fetchone()

            if result is not None:
                # Cập nhật trạng thái thành "cần đồng bộ" (0)
                cursor.execute(
                    "UPDATE sync_status SET sync_status = 0, last_classification = ? WHERE brand_code = ?",
                    (classification, brand_code)
                )
            else:
                # Thêm mới với trạng thái "cần đồng bộ" (0)
                cursor.execute(
                    "INSERT INTO sync_status (brand_code, sync_status, last_classification, spreadsheet_id) VALUES (?, 0, ?, ?)",
                    (brand_code, classification, spreadsheet_id)
                )
        except Exception as e:
            print(f"Lỗi khi cập nhật trạng thái đồng bộ: {e}")

        # Commit transaction
        conn.commit()
        conn.close()
    except Exception as e:
        print(f"Error saving product classification: {e}")

def get_product_classifications(brand_code):
    """Get all product classifications for a brand code"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        cursor.execute(
            "SELECT product_name, classification FROM product_classifications WHERE brand_code = ?",
            (brand_code,)
        )
        products = cursor.fetchall()
        conn.close()
        return products
    except Exception as e:
        print(f"Error getting product classifications: {e}")
        return []

def get_price_history(product_name):
    """Lấy lịch sử giá của sản phẩm theo thời gian"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()

        # Lấy lịch sử giá sắp xếp theo thời gian
        cursor.execute(
            "SELECT price, timestamp, live_session FROM price_history WHERE product_name = ? ORDER BY timestamp",
            (product_name,)
        )
        price_history = cursor.fetchall()

        # Lấy giá hiện tại từ bảng product_classifications
        cursor.execute(
            "SELECT price FROM product_classifications WHERE product_name = ?",
            (product_name,)
        )
        current_price_result = cursor.fetchone()
        current_price = current_price_result[0] if current_price_result else 0

        conn.close()

        return price_history, current_price
    except Exception as e:
        print(f"Error getting price history: {e}")
        return [], 0

def save_price_threshold(value):
    """Save the price threshold to settings"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        cursor.execute(
            "INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)",
            ("price_threshold", str(value))
        )
        conn.commit()
        conn.close()
    except Exception as e:
        print(f"Error saving price threshold: {e}")

def is_luxury_brand(product_name):
    """Check if a product belongs to a luxury brand"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()

        # Get all luxury brands
        cursor.execute("SELECT brand_name FROM high_end_brands WHERE is_active = 1")
        luxury_brands = [row[0].lower() for row in cursor.fetchall()]
        conn.close()

        # Check if product name contains any luxury brand
        product_lower = product_name.lower()
        for brand in luxury_brands:
            if brand.lower() in product_lower:
                return True

        return False
    except Exception as e:
        print(f"Error checking luxury brand: {e}")
        return False

def update_product_price(product_name, new_price, price_date=None):
    """Cập nhật giá mới cho sản phẩm"""
    try:
        # Kiểm tra giá trị hợp lý (dưới 100 triệu)
        MAX_REASONABLE_PRICE = 100000000  # 100 triệu
        if new_price > MAX_REASONABLE_PRICE:
            # Có thể là lỗi định dạng, thử chia cho 1000
            if new_price % 1000 == 0:
                print(f"Giá quá cao ({new_price}), tự động chia cho 1000 -> {new_price//1000}")
                new_price = new_price // 1000
            else:
                # Nếu không thể chia đều cho 1000, có thể là lỗi nhập liệu
                print(f"Cảnh báo: Giá nhập vào ({new_price}) vượt quá 100 triệu đồng")

        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()

        # Bắt đầu transaction
        cursor.execute("BEGIN TRANSACTION")

        # Cập nhật giá hiện tại
        cursor.execute(
            "UPDATE product_classifications SET price = ? WHERE product_name = ?",
            (new_price, product_name)
        )

        # Thêm vào lịch sử giá
        if price_date is None:
            price_date = datetime.now().strftime("%d.%m.%Y")

        cursor.execute(
            "INSERT INTO price_history (product_name, price, price_date, source) VALUES (?, ?, ?, ?)",
            (product_name, new_price, price_date, "Manual Update")
        )

        conn.commit()
        conn.close()
        return True
    except Exception as e:
        print(f"Error updating product price: {e}")
        return False

def excel_column_to_index(column_name):
    """Chuyển đổi tên cột Excel (A, B, AA, AB...) thành chỉ số (0, 1, 26, 27...)"""
    if not column_name:
        return -1

    column_name = column_name.upper()
    result = 0
    for c in column_name:
        result = result * 26 + (ord(c) - ord('A') + 1)
    return result - 1

def auto_update_prices(spreadsheet, deal_sheet_name=None, deal_product_col=None,
                       deal_price_col=None, log_callback=None, progress_callback=None,
                       live_session=None):
    """Tự động cập nhật giá từ Deal list trong Google Spreadsheet

    Args:
        spreadsheet: Đối tượng Google Spreadsheet
        deal_sheet_name: Tên sheet chứa Deal list
        deal_product_col: Tên cột chứa tên sản phẩm
        deal_price_col: Tên cột chứa giá sản phẩm
        log_callback: Hàm callback để ghi log
        progress_callback: Hàm callback để cập nhật tiến trình
        live_session: Phiên Live dạng d.m (ví dụ: "15.6")

    Returns:
        dict: Kết quả cập nhật
    """
    try:
        # Hàm ghi log nếu được cung cấp
        def log(message):
            if log_callback:
                log_callback(message)
            else:
                print(message)

        log("Bắt đầu quét tự động cập nhật giá...")

        # Đảm bảo spreadsheet và deal_sheet_name hợp lệ
        if not spreadsheet:
            log("Lỗi: Spreadsheet chưa được tải")
            return {"error": "Spreadsheet chưa được tải"}

        # Nếu live_session không được cung cấp, trích xuất từ tên spreadsheet
        if not live_session:
            live_session, spreadsheet_name = extract_live_session_from_spreadsheet(spreadsheet)
            log(f"Đang xử lý phiên live: {live_session} (Từ spreadsheet: {spreadsheet_name})")
        else:
            log(f"Đang xử lý phiên live: {live_session} (Do người dùng chọn)")
            # Lấy tên spreadsheet nếu cần
            spreadsheet_name = ""
            if hasattr(spreadsheet, 'title'):
                spreadsheet_name = spreadsheet.title

        # Lấy spreadsheet_id
        spreadsheet_id = ""
        if hasattr(spreadsheet, 'id'):
            spreadsheet_id = spreadsheet.id

        # Thiết lập giá trị mặc định nếu không được cung cấp
        if not deal_sheet_name:
            deal_sheet_name = "Deal list"  # Tên mặc định của sheet chứa Deal list

        if not deal_product_col:
            deal_product_col = "H"  # Cột mặc định chứa tên sản phẩm

        if not deal_price_col:
            deal_price_col = "AB"  # Cột mặc định chứa giá

        # Truy cập Deal list
        try:
            deal_sheet = spreadsheet.worksheet(deal_sheet_name)
            log(f"Đã truy cập worksheet: {deal_sheet_name}")
        except Exception as e:
            log(f"Không tìm thấy sheet '{deal_sheet_name}', đang tìm kiếm thay thế...")

            # Tìm sheet có tên tương tự
            sheet_list = spreadsheet.worksheets()
            similar_sheets = []

            for sheet in sheet_list:
                sheet_name = sheet.title.lower()
                if "deal" in sheet_name or "giá" in sheet_name or "price" in sheet_name:
                    similar_sheets.append(sheet.title)

            if similar_sheets:
                deal_sheet_name = similar_sheets[0]
                log(f"Đã tìm thấy sheet thay thế: {deal_sheet_name}")
                deal_sheet = spreadsheet.worksheet(deal_sheet_name)
            else:
                log(f"Lỗi: Không tìm thấy sheet Deal list phù hợp")
                return {"error": f"Không tìm thấy sheet Deal list phù hợp"}

        # Lấy tất cả dữ liệu từ Deal list
        deal_all = deal_sheet.get_all_values()
        if len(deal_all) < 4:
            log("Không có dữ liệu trong Deal list.")
            return {"error": "Không có dữ liệu trong Deal list."}

        deal_data = deal_all[3:]  # Bỏ 3 dòng tiêu đề
        log(f"Đã tìm thấy {len(deal_data)} dòng dữ liệu trong Deal list")

        idx_product = excel_column_to_index(deal_product_col)
        idx_price = excel_column_to_index(deal_price_col)

        # Kiểm tra chỉ số hợp lệ
        if idx_product < 0 or idx_price < 0:
            log(f"Lỗi: Chỉ số cột không hợp lệ: Product={idx_product}, Price={idx_price}")
            return {"error": f"Tên cột không hợp lệ: Product={deal_product_col}, Price={deal_price_col}"}

        # Kết nối database
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()

        # Lấy tất cả sản phẩm từ database để so sánh
        cursor.execute("SELECT product_name, price FROM product_classifications")
        db_products = {row[0]: row[1] for row in cursor.fetchall()}

        log(f"Đã tìm thấy {len(db_products)} sản phẩm trong database")

        # Bắt đầu quét và cập nhật
        updated_products = []
        scanned_count = 0
        updated_count = 0

        for i, row in enumerate(deal_data):
            # Báo cáo tiến độ mỗi 10%
            if i % max(1, len(deal_data) // 10) == 0 and progress_callback:
                progress = min(99, int(i / len(deal_data) * 100))
                progress_callback(progress)

            try:
                # Kiểm tra độ dài của row để tránh lỗi index
                if not row or len(row) <= max(idx_product, idx_price):
                    continue

                # Trích xuất dữ liệu
                product_name = row[idx_product].strip()
                if not product_name:
                    continue

                # Thêm kiểm tra cột N - Pick (chỉ xử lý nếu có "Yes")
                idx_pick = 11  # Cột N (0-based index)
                pick_value = row[idx_pick].strip() if len(row) > idx_pick else ""

                if pick_value.lower() != "yes":
                    continue

                # Lấy giá từ Deal list
                price_text = row[idx_price].strip() if len(row) > idx_price else ""
                if not price_text:
                    continue

                # Chuyển đổi giá
                cleaned_price = re.sub(r'[^\d]', '', price_text)
                if not cleaned_price:
                    continue

                sheet_price = int(cleaned_price)

                # Kiểm tra giá trị hợp lý (dưới 100 triệu)
                MAX_REASONABLE_PRICE = 100000000  # 100 triệu
                if sheet_price > MAX_REASONABLE_PRICE:
                    # Có thể là lỗi định dạng, thử chia cho 1000
                    if sheet_price % 1000 == 0:
                        log(f"Giá quá cao cho '{product_name}' ({sheet_price}), tự động chia cho 1000 -> {sheet_price//1000}")
                        sheet_price = sheet_price // 1000

                scanned_count += 1

                # Kiểm tra nếu sản phẩm có trong database
                if product_name in db_products:
                    db_price = db_products[product_name]

                    # So sánh giá (cho phép sai số 5% để tránh cập nhật không cần thiết)
                    price_diff_percent = abs(sheet_price - db_price) / max(1, db_price) * 100

                    # Nếu giá thay đổi > 5% hoặc > 10,000đ
                    if (price_diff_percent > 5 or abs(sheet_price - db_price) > 10000) and sheet_price > 0:
                        # Sử dụng thông tin phiên live thay vì ngày hiện tại
                        price_date = live_session

                        cursor.execute("BEGIN TRANSACTION")

                        # Cập nhật giá hiện tại
                        cursor.execute(
                            "UPDATE product_classifications SET price = ? WHERE product_name = ?",
                            (sheet_price, product_name)
                        )

                        # Tạo nguồn với thông tin đầy đủ
                        source = f"{deal_sheet_name} - {spreadsheet_name}"
                        if spreadsheet_id:
                            short_id = spreadsheet_id[-8:] if len(spreadsheet_id) > 8 else spreadsheet_id
                            source = f"{deal_sheet_name} ({short_id}) - {spreadsheet_name}"

                        # Thêm vào lịch sử giá với thông tin phiên live
                        cursor.execute(
                            "INSERT INTO price_history (product_name, price, price_date, source) VALUES (?, ?, ?, ?)",
                            (product_name, sheet_price, price_date, source)
                        )

                        cursor.execute("COMMIT")

                        updated_count += 1
                        updated_products.append({
                            "name": product_name,
                            "old_price": db_price,
                            "new_price": sheet_price,
                            "change_percent": price_diff_percent,
                            "live_session": price_date
                        })

                        log(f"Đã cập nhật giá cho '{product_name}': {format_price_vnd(db_price)}đ -> {format_price_vnd(sheet_price)}đ ({price_diff_percent:.1f}%) [Phiên live: {price_date}]")
            except Exception as e:
                log(f"Lỗi xử lý dòng {i+3} trong Deal list: {str(e)}")
                continue

        # Hoàn tất và báo cáo
        conn.close()

        if progress_callback:
            progress_callback(100)

        log(f"Hoàn tất cập nhật giá tự động. Đã quét {scanned_count} sản phẩm, cập nhật {updated_count} sản phẩm.")

        return {
            "scanned": scanned_count,
            "updated": updated_count,
            "products": updated_products,
            "live_session": live_session,
            "spreadsheet_name": spreadsheet_name
        }
    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        if log_callback:
            log_callback(f"Lỗi khi tự động cập nhật giá: {str(e)}")
            log_callback(f"Chi tiết lỗi: {error_traceback}")
        return {"error": str(e), "detail": error_traceback}

def get_similarity_threshold():
    """Lấy ngưỡng tương đồng từ database"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        cursor.execute("SELECT value FROM settings WHERE key = 'similarity_threshold'")
        result = cursor.fetchone()
        conn.close()

        if result:
            return float(result[0])
        else:
            # Giá trị mặc định
            return 0.85
    except Exception as e:
        print(f"Error getting similarity threshold: {e}")
        return 0.85

def save_similarity_threshold(threshold):
    """Lưu ngưỡng tương đồng vào database"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        cursor.execute(
            "INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)",
            ("similarity_threshold", str(threshold))
        )
        conn.commit()
        conn.close()
    except Exception as e:
        print(f"Error saving similarity threshold: {e}")

def get_all_classifications():
    """Lấy tất cả phân loại từ database"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()

        # Lấy thống kê phân loại theo brand
        cursor.execute("""
            SELECT brand_code, classification, COUNT(*) as product_count
            FROM product_classifications
            GROUP BY brand_code, classification
            ORDER BY brand_code, classification
        """)

        classifications = []
        for row in cursor.fetchall():
            classifications.append({
                'brand_code': row[0],
                'classification': row[1],
                'product_count': row[2]
            })

        conn.close()
        return classifications
    except Exception as e:
        print(f"Error getting classifications: {e}")
        return []

def get_database_stats():
    """Lấy thống kê tổng quan từ database"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()

        # Đếm tổng số sản phẩm
        cursor.execute("SELECT COUNT(*) FROM product_classifications")
        total_products = cursor.fetchone()[0]

        # Đếm tổng số brands
        cursor.execute("SELECT COUNT(DISTINCT brand_code) FROM product_classifications")
        total_brands = cursor.fetchone()[0]

        # Đếm tổng số classifications khác nhau
        cursor.execute("SELECT COUNT(DISTINCT classification) FROM product_classifications")
        total_classifications = cursor.fetchone()[0]

        # Đếm entries trong price history
        cursor.execute("SELECT COUNT(*) FROM price_history")
        price_history_entries = cursor.fetchone()[0]

        conn.close()

        return {
            'total_products': total_products,
            'total_brands': total_brands,
            'total_classifications': total_classifications,
            'price_history_entries': price_history_entries
        }
    except Exception as e:
        print(f"Error getting database stats: {e}")
        return {
            'total_products': 0,
            'total_brands': 0,
            'total_classifications': 0,
            'price_history_entries': 0
        }

def process_brand_classifications(spreadsheet, brand_sheet_name, deal_sheet_name,
                                brand_code_col, brand_type_col, deal_brand_code_col,
                                deal_product_col, last_row, model, brand_name_col="D",
                                deal_price_col="AB", deal_pick_col="N",
                                log_callback=None, progress_callback=None):
    """
    Xử lý phân loại cho tất cả brands trong spreadsheet

    Returns:
        dict: Kết quả xử lý với thông tin chi tiết
    """
    global api_call_count, total_tokens_used

    def log(message):
        if log_callback:
            log_callback(message)
        else:
            print(message)

    try:
        # Reset counters
        api_call_count = 0
        total_tokens_used = 0

        # Truy cập worksheets
        brand_sheet = spreadsheet.worksheet(brand_sheet_name)
        deal_sheet = spreadsheet.worksheet(deal_sheet_name)

        # Lấy dữ liệu
        brand_all = brand_sheet.get_all_values()
        if len(brand_all) < 2:
            return {"error": "Không có dữ liệu trong Brand list."}

        # Xử lý đến dòng được chỉ định
        try:
            last = int(last_row) if last_row else len(brand_all)
            brand_data = brand_all[1:last]  # Bỏ tiêu đề
        except Exception:
            brand_data = brand_all[1:]

        deal_all = deal_sheet.get_all_values()
        if len(deal_all) < 4:
            return {"error": "Không có dữ liệu trong Deal list."}

        deal_data = deal_all[3:]  # Bỏ 3 dòng tiêu đề

        # Tạo mapping: Brand code -> danh sách sản phẩm
        deal_mapping = {}
        for row in deal_data:
            try:
                idx_brand = excel_column_to_index(deal_brand_code_col)
                idx_product = excel_column_to_index(deal_product_col)
                idx_price = excel_column_to_index(deal_price_col)
                idx_pick = excel_column_to_index(deal_pick_col)

                if len(row) <= max(idx_brand, idx_product, idx_price, idx_pick):
                    continue

                brand_code = row[idx_brand].strip()
                product_name = row[idx_product].strip()

                # Kiểm tra cột Pick
                pick_value = row[idx_pick].strip() if len(row) > idx_pick else ""
                if pick_value.lower() != "yes":
                    continue

                # Đọc giá sản phẩm
                price_value = 0
                if len(row) > idx_price:
                    try:
                        price_text = row[idx_price].strip()
                        cleaned_price = re.sub(r'[^\d]', '', price_text)
                        if cleaned_price:
                            price_value = int(cleaned_price)
                    except Exception:
                        price_value = 0

                if brand_code and product_name:
                    if brand_code not in deal_mapping:
                        deal_mapping[brand_code] = []
                    deal_mapping[brand_code].append({
                        "name": product_name,
                        "price": price_value
                    })
            except Exception as e:
                log(f"Lỗi đọc dòng trong Deal list: {e}")
                continue

        # Xử lý từng brand
        results = []
        processed_count = 0
        updated_count = 0

        # Lấy live session từ spreadsheet
        live_session, spreadsheet_name = extract_live_session_from_spreadsheet(spreadsheet)

        for i, row in enumerate(brand_data, start=2):
            if progress_callback:
                progress = int((i-1) / len(brand_data) * 100)
                progress_callback(progress)

            try:
                idx_brand = excel_column_to_index(brand_code_col)
                idx_type = excel_column_to_index(brand_type_col)
                idx_brand_name = excel_column_to_index(brand_name_col)

                if len(row) <= max(idx_brand, idx_type, idx_brand_name):
                    continue

                brand_code = row[idx_brand].strip()
                brand_name = row[idx_brand_name].strip()

                if not brand_code:
                    log(f"Dòng {i}: Brand code trống. Dừng xử lý.")
                    break

                if brand_code not in deal_mapping:
                    continue

                product_items = deal_mapping[brand_code]
                log(f"Đang phân loại {len(product_items)} sản phẩm cho brand {brand_code}...")

                classifications_with_priority = []
                product_details = []

                for product_item in product_items:
                    product = product_item["name"]
                    price = product_item.get("price", 0)

                    # Gọi hàm phân loại
                    result, _, extracted_data = extract_product_category(product, price=price, model=model)
                    if result:
                        classifications_with_priority.append({"category": result})
                        product_details.append((product, result))

                        # Lưu vào database
                        save_product_classification(product, result, brand_code, price,
                                                  deal_sheet_name, spreadsheet.id if hasattr(spreadsheet, 'id') else "",
                                                  live_session)
                        processed_count += 1

                # Xác định phân loại chính cho brand
                if classifications_with_priority:
                    category_counts = {}
                    for item in classifications_with_priority:
                        category = item["category"]
                        category_counts[category] = category_counts.get(category, 0) + 1

                    # Sắp xếp theo số lượng
                    sorted_categories = sorted(category_counts.items(), key=lambda x: x[1], reverse=True)
                    main_classification = sorted_categories[0][0]

                    # Tạo chuỗi tóm tắt
                    if len(sorted_categories) == 1:
                        classification_summary = main_classification
                    else:
                        classification_summary = f"{main_classification} ({sorted_categories[0][1]})"
                        if len(sorted_categories) > 1:
                            others = [f"{cat} ({count})" for cat, count in sorted_categories[1:3]]
                            classification_summary += f", {', '.join(others)}"

                    results.append({
                        'brand_code': brand_code,
                        'classification': classification_summary,
                        'products': [p[0] for p in product_details]
                    })

                    updated_count += 1
                    log(f"Brand code '{brand_code}' -> {classification_summary}")

            except Exception as e:
                log(f"Lỗi xử lý brand {brand_code}: {e}")
                continue

        if progress_callback:
            progress_callback(100)

        log(f"Hoàn tất xử lý. Đã xử lý {processed_count} sản phẩm, cập nhật {updated_count} brands.")

        return {
            "success": True,
            "processed_count": processed_count,
            "updated_count": updated_count,
            "api_calls": api_call_count,
            "tokens_used": total_tokens_used,
            "classifications": results,
            "live_session": live_session,
            "spreadsheet_name": spreadsheet_name
        }

    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        log(f"Lỗi trong quá trình xử lý: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "detail": error_traceback
        }
