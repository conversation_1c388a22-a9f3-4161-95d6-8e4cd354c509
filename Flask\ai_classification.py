import sys, re, time, json, base64, pickle, os, sqlite3, logging
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.figure import Figure
from pathlib import Path
from collections import deque
from datetime import datetime
from difflib import SequenceMatcher
from openai import OpenAI
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QDialog,
    QGroupBox, QLabel, QLineEdit, QPushButton, QComboBox, QMessageBox, QFormLayout,
    QTextEdit, QTableWidget, QTableWidgetItem, QHeaderView, QPushButton, QProgressBar, QSizePolicy, QMenu, QToolButton, QCheckBox, QDateEdit, QGridLayout, QTabWidget, QFileDialog
)
from PyQt6.QtCore import Qt, QObject, QThread, pyqtSignal, QTimer, QEvent, Q<PERSON><PERSON><PERSON><PERSON>ool, QDate, QMutex
from PyQt6.QtGui import QColor

# Cấu hình logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

# Tạo handler để ghi log vào file
def setup_logger():
    log_file = Path(get_data_directory()) / "app.log"
    file_handler = logging.FileHandler(str(log_file))
    file_handler.setLevel(logging.DEBUG)

    # Tạo handler để hiển thị log trên console
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # Định dạng log
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # Thêm handler vào logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

# Import module quản lý Google Sheets từ file gsheet_manager.py (bạn đã có)
from gsheet_manager import GoogleSheetManager
from thread_pool_fix import ProcessingRunnable

# -------------------- CONFIG --------------------
OAUTH2_BASE64 = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
# Khởi tạo client OpenAI sử dụng phiên bản API mới
openai_client = OpenAI(api_key="********************************************************************************************************************************************************************")

# Thời gian chờ giữa các lần gọi API (giây)
API_CALL_DELAY = 0.5

# Biến toàn cục để theo dõi số lần gọi API và số token đã sử dụng
api_call_count = 0
total_tokens_used = 0

# -------------------- END CONFIG --------------------

# Global exception handler để tránh crash đột ngột
import traceback
import sys

def global_exception_handler(exctype, value, tb):
    error_msg = ''.join(traceback.format_exception(exctype, value, tb))
    print(f"GLOBAL EXCEPTION: {error_msg}")
    # Lưu lỗi vào file
    try:
        with open(str(DATA_DIR / "error_log.txt"), "a", encoding="utf-8") as f:
            f.write(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] GLOBAL EXCEPTION:\n")
            f.write(error_msg)
            f.write("\n" + "-"*50 + "\n")
    except:
        pass
    # Gọi handler mặc định
    sys.__excepthook__(exctype, value, tb)

# Cài đặt global exception handler
sys.excepthook = global_exception_handler

# Định nghĩa đường dẫn đến thư mục lưu trữ dữ liệu
def get_data_directory():
    """Tạo và trả về đường dẫn đến thư mục lưu trữ dữ liệu cho AI Classification"""
    try:
        app_data_path = os.environ.get('LOCALAPPDATA', '')
        if not app_data_path:
            app_data_path = str(Path.home() / "AppData" / "Local")
            if not os.path.exists(app_data_path):
                # Nếu không tìm thấy LOCALAPPDATA, thử tạo thư mục trong Documents
                documents_path = os.path.join(str(Path.home()), "Documents")
                app_data_path = os.path.join(documents_path, "Data All in One Data")
                os.makedirs(app_data_path, exist_ok=True)

        data_dir = Path(app_data_path) / "Data All in One" / "AI Classification"

        # Tạo thư mục nếu chưa tồn tại
        os.makedirs(data_dir, exist_ok=True)

        # Kiểm tra quyền ghi
        test_file = data_dir / "test_write_permission.txt"
        try:
            with open(test_file, 'w') as f:
                f.write("Test write permission")
            os.remove(test_file)
            print(f"Thư mục dữ liệu đã sẵn sàng: {data_dir}")
        except Exception as e:
            print(f"Không có quyền ghi vào thư mục {data_dir}: {e}")
            # Thử tạo thư mục thay thế trong Documents
            documents_path = os.path.join(str(Path.home()), "Documents")
            alt_data_dir = Path(documents_path) / "Data All in One" / "AI Classification"
            os.makedirs(alt_data_dir, exist_ok=True)
            print(f"Đã tạo thư mục thay thế: {alt_data_dir}")
            return alt_data_dir

        return data_dir
    except Exception as e:
        print(f"Lỗi khi tạo thư mục dữ liệu: {e}")
        # Trả về thư mục trong Documents làm backup
        documents_path = os.path.join(str(Path.home()), "Documents")
        fallback_dir = Path(documents_path) / "Data All in One" / "AI Classification"
        os.makedirs(fallback_dir, exist_ok=True)
        return fallback_dir

DATA_DIR = get_data_directory()
# Khởi tạo logger
setup_logger()
FEEDBACK_FILE = DATA_DIR / "feedback.json"

PROMPT_TEMPLATE = (
    "Bạn là một chuyên gia phân loại và trích xuất thông tin sản phẩm với khả năng hiểu tiếng Việt chuyên sâu. "
    "Từ thông tin sản phẩm dưới đây, hãy phân tích và trả về các thông tin sau: \n\n"
    "{price_info}"
    "Tên sản phẩm: {product_description}\n\n"
    "Luật trích xuất chi tiết:\n"
    "1. **type_of_product**: loại sản phẩm chính (VD: 'nồi chiên không dầu', 'sữa rửa mặt', 'kem chống nắng'...).\n"
    "   Quy tắc đặc biệt cho type_of_product:\n"
    "   • CHỈ và CHỈ KHI là nước uống/thức uống collagen → 'nước uống collagen'\n"
    "   • camera/camera wifi/camera ip → 'camera'\n"
    "   • tinh chất/serum/essence → 'serum'\n"
    "   • tẩy da chết/scrub/exfoliator → 'tẩy da chết'\n"
    "   • điện thoại/smartphone → 'điện thoại'\n"
    "   • tã/bỉm → 'tã'\n"
    "   • kit trắng răng/trắng răng → 'kit trắng răng'\n"
    "   • túi xách/túi đeo vai → 'túi xách'\n"
    "   • máy lọc không khí/lọc không khí → 'máy lọc không khí'\n"
    "   • CHỈ và CHỈ KHI chứa chính xác từ khóa: áo ngực/quần lót nữ/bra/áo lót → 'đồ lót nữ'\n"
    "   • son/lipstick/son tint/balm/kem môi → 'son môi'\n"
    "   • phấn mắt/mascara/eyeliner/kẻ mắt/má hồng/eyeshadow/phấn → 'makeup'\n"
    "   • mặt nạ/mask/mặt nạ đất sét/mặt nạ giấy/mặt nạ ngủ → 'mặt nạ dưỡng da'\n"
    "   • nước cân bằng/toner/nước hoa hồng → 'toner'\n"
    "2. **brand**: tên thương hiệu nếu có (VD: 'LocknLock', 'Anessa'); nếu không có => chuỗi rỗng.\n"
    "3. **product_name**: phần tên riêng của sản phẩm. (VD 'Máy ép chậm Sunhouse SHD5515', 'Máy lọc không khí Sunhouse SHD-15AP9715')\n"
    "4. **code**: mã sản phẩm (VD 'F9', 'EH015', 'MC210K', 'SHD-15AP9715', ...). Nếu không tìm thấy => chuỗi rỗng.\n"
    "5. **capacity**: dung tích, size, công suất, v.v. (VD '50ml', '250g', 'size 41', '1000W', '10000mAh').\n"
    "6. **quantity**: số lượng sản phẩm trong combo/bộ (mặc định là 1).\n\n"
    "Hãy trả lời theo định dạng sau (KHÔNG trả về dạng JSON):\n"
    "Phân loại: [type_of_product]\n"
    "Brand: [brand]\n"
    "Tên sản phẩm: [product_name]\n"
    "Mã sản phẩm: [code]\n"
    "Dung tích/Kích thước: [capacity]\n"
    "Số lượng: [quantity]"
)

# Database setup
def setup_database():
    """Initialize SQLite database for active learning storage"""
    db_path = DATA_DIR / "active_learning.db"
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()

    # Create feedback table with product names
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS feedback (
        brand_code TEXT,
        classification TEXT,
        product_names TEXT,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (brand_code)
    )
    ''')

    # Create product classification table for individual product mappings with price
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS product_classifications (
        product_name TEXT,
        classification TEXT,
        brand_code TEXT,
        price INTEGER DEFAULT 0,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (product_name)
    )
    ''')

    # Create similar product classification table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS similar_products (
        product_name TEXT,
        similar_to TEXT,
        classification TEXT,
        similarity_score REAL,
        price INTEGER DEFAULT 0,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (product_name)
    )
    ''')

    # Tạo bảng lưu trữ brands (thương hiệu)
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS brands (
        brand_code TEXT PRIMARY KEY,
        brand_name TEXT,
        classification TEXT,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Tạo bảng lưu trữ thiết lập
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Tạo bảng lưu trữ lịch sử giá với thông tin nguồn và ngày
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS price_history (
        product_name TEXT,
        price INTEGER,
        live_session TEXT,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (product_name, timestamp)
    )
    ''')

    # Tạo bảng sync_status để theo dõi trạng thái đồng bộ lên Google Sheets
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS sync_status (
        brand_code TEXT PRIMARY KEY,
        sync_status INTEGER DEFAULT 0,
        last_sync_time TIMESTAMP,
        last_classification TEXT,
        spreadsheet_id TEXT
    )
    ''')

    conn.commit()
    return conn

# Initialize database connection
DB_PATH = DATA_DIR / "active_learning.db"

# Migrate existing data if needed
def migrate_json_to_sqlite():
    """Migrate existing JSON data to SQLite if available"""
    try:
        # Migrate feedback data
        if FEEDBACK_FILE.exists():
            conn = sqlite3.connect(str(DB_PATH))
            cursor = conn.cursor()
            with open(FEEDBACK_FILE, "r", encoding="utf-8") as f:
                feedback_data = json.load(f)
                for brand_code, classification in feedback_data.items():
                    cursor.execute(
                        "INSERT OR REPLACE INTO feedback (brand_code, classification) VALUES (?, ?)",
                        (brand_code, classification)
                    )
            conn.commit()
    except Exception as e:
        print(f"Migration error: {e}")

# Initialize database and migrate existing data
if not DB_PATH.exists():
    setup_database()
    migrate_json_to_sqlite()
else:
    # Verify all tables exist and create any missing ones
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()

        # Check if similar_products table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='similar_products'")
        if not cursor.fetchone():
            # Create missing table if it doesn't exist
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS similar_products (
                product_name TEXT,
                similar_to TEXT,
                classification TEXT,
                similarity_score REAL,
                price INTEGER DEFAULT 0,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (product_name)
            )
            ''')
            print("Created missing similar_products table")
        conn.commit()
        conn.close()
    except Exception as e:
        print(f"Database verification error: {e}")

# Thêm hàm xử lý lỗi người dùng
def show_user_friendly_error(parent, technical_error, user_message=None):
    """Hiển thị thông báo lỗi thân thiện với người dùng"""
    if user_message is None:
        user_message = "Chương trình gặp sự cố khi xử lý dữ liệu."

    error_dialog = QMessageBox(parent)
    error_dialog.setWindowTitle("Thông báo")
    error_dialog.setText(user_message)
    error_dialog.setIcon(QMessageBox.Icon.Warning)

    # Thêm chi tiết kỹ thuật vào nút Details
    error_dialog.setDetailedText(f"Chi tiết lỗi kỹ thuật: {str(technical_error)}")

    # Thêm hướng dẫn
    error_dialog.setInformativeText("Vui lòng thử lại hoặc liên hệ hỗ trợ kỹ thuật nếu lỗi tiếp tục xảy ra.")

    error_dialog.exec()

classification_cache = {}

def post_process(result: str) -> str:
    """Parse the result string to extract proper category name"""
    # Remove excessive spaces and lowercase the text
    text = result.strip().lower()

    # Dictionary for common phrases to normalize
    if ("nước uống collagen" in text or
        "thức uống collagen" in text or
        "collagen uống" in text or
        ("nước uống" in text and "collagen" in text) or
        ("thức uống" in text and "collagen" in text)):
        return "nước uống collagen"

    if any(term in text for term in ["camera", "camera wifi", "camera ip"]):
        return "camera"

    if any(term in text for term in ["tinh chất", "serum", "essence"]):
        return "serum"

    if any(term in text for term in ["tẩy da chết", "scrub", "exfoliator"]):
        return "tẩy da chết"

    if any(term in text for term in ["điện thoại", "smartphone"]):
        return "điện thoại"

    if any(term in text for term in ["tã", "bỉm"]):
        return "tã"

    if any(term in text for term in ["kit trắng răng", "trắng răng"]):
        return "kit trắng răng"

    if any(term in text for term in ["túi xách", "túi đeo vai"]):
        return "túi xách"

    if any(term in text for term in ["máy lọc không khí", "lọc không khí"]):
        return "máy lọc không khí"

    # Kiểm tra CHỈ và CHỈ KHI chứa chính xác từ khóa cho "Đồ lót nữ"
    exact_keywords = ["áo ngực", "quần lót nữ", "bra", "áo lót"]
    for keyword in exact_keywords:
        if keyword in text:
            return "đồ lót nữ"
    # Loại trừ các loại áo khác không phải đồ lót
    if "áo" in text and not any(keyword in text for keyword in exact_keywords):
        # Không phân loại là đồ lót nữ nếu chỉ chứa từ "áo" mà không có từ khóa chính xác
        pass

    if any(term in text for term in ["son", "lipstick", "son tint", "balm", "kem môi"]):
        return "son môi"

    if any(term in text for term in ["phấn mắt", "mascara", "eyeliner", "kẻ mắt", "má hồng", "eyeshadow", "phấn"]):
        return "makeup"

    if any(term in text for term in ["mặt nạ", "mask", "mặt nạ đất sét", "mặt nạ giấy", "mặt nạ ngủ"]):
        return "mặt nạ dưỡng da"

    if any(term in text for term in ["nước cân bằng", "toner", "nước hoa hồng"]):
        return "toner"

    # Get first line if multiple lines
    if '\n' in result:
        text = result.split('\n')[0].strip()

    # Trả về chuỗi chữ thường thay vì viết hoa
    return text.strip().lower()

def string_similarity(str1, str2):
    """Calculate similarity between two strings"""
    return SequenceMatcher(None, str1.lower(), str2.lower()).ratio()

def find_similar_product(product_name, threshold=0.85):
    """
    Finds the most similar product in the database
    Returns tuple (product_name, similarity_score) or None if no match
    """
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()

        # Kiểm tra bảng similar_products có tồn tại không
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='similar_products'")
        if not cursor.fetchone():
            # Tạo bảng nếu chưa tồn tại
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS similar_products (
                product_name TEXT,
                similar_to TEXT,
                classification TEXT,
                similarity_score REAL,
                price INTEGER DEFAULT 0,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (product_name)
            )
            ''')
            conn.commit()
            # Không có dữ liệu trong bảng mới tạo
            conn.close()
            return None

        # Lấy tất cả sản phẩm từ database
        cursor.execute("SELECT product_name FROM product_classifications")
        stored_products = [row[0] for row in cursor.fetchall()]

        # Không có sản phẩm nào trong database
        if not stored_products:
            conn.close()
            return None

        # Tìm sản phẩm giống nhất
        best_similarity = 0
        best_match = None

        for stored_product in stored_products:
            similarity = string_similarity(product_name.lower(), stored_product.lower())
            if similarity > best_similarity and similarity >= threshold:
                best_similarity = similarity
                best_match = stored_product

        conn.close()

        if best_match:
            return (best_match, best_similarity)
        return None
    except Exception as e:
        print(f"Error finding similar product: {e}")
        try:
            conn.close()
        except:
            pass
        return None

def check_and_update_table_structure():
    """Check and update the database structure if needed"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()

        # Kiểm tra bảng product_classifications
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='product_classifications'")
        if not cursor.fetchone():
            print("Creating product_classifications table")
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS product_classifications (
                product_name TEXT,
                classification TEXT,
                brand_code TEXT,
                price INTEGER DEFAULT 0,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (product_name)
            )
            ''')

        # Kiểm tra bảng brands
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='brands'")
        if not cursor.fetchone():
            print("Creating brands table")
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS brands (
                brand_code TEXT PRIMARY KEY,
                brand_name TEXT,
                classification TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')

        # Check for the price column in the product_classifications table
        cursor.execute("PRAGMA table_info(product_classifications)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]

        if "price" not in column_names:
            print("Adding price column to product_classifications table")
            cursor.execute("ALTER TABLE product_classifications ADD COLUMN price INTEGER DEFAULT 0")

        # Check for the price_history table
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='price_history'")
        if not cursor.fetchone():
            print("Creating price_history table")
            cursor.execute('''
            CREATE TABLE price_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_name TEXT,
                price INTEGER,
                live_session TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
        else:
            # Kiểm tra xem bảng price_history có cột live_session chưa
            cursor.execute("PRAGMA table_info(price_history)")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]

            # Nếu chưa có cột live_session nhưng có cột source
            if "live_session" not in column_names and "source" in column_names:
                print("Đổi tên cột source thành live_session trong bảng price_history")
                # SQLite không hỗ trợ RENAME COLUMN trực tiếp, cần tạo bảng mới và sao chép dữ liệu
                cursor.execute('''
                CREATE TABLE price_history_new (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_name TEXT,
                    price INTEGER,
                    live_session TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                ''')

                # Sao chép dữ liệu từ bảng cũ sang bảng mới
                cursor.execute('''
                INSERT INTO price_history_new (id, product_name, price, live_session, timestamp)
                SELECT id, product_name, price, source, timestamp FROM price_history
                ''')

                # Xóa bảng cũ và đổi tên bảng mới
                cursor.execute("DROP TABLE price_history")
                cursor.execute("ALTER TABLE price_history_new RENAME TO price_history")
                print("Đã cập nhật cấu trúc bảng price_history")

            # Nếu có cả hai cột, giữ nguyên nhưng đảm bảo live_session được cập nhật
            if "live_session" in column_names and "source" in column_names:
                print("Cập nhật giá trị từ cột source sang live_session nếu cần")
                cursor.execute('''
                UPDATE price_history
                SET live_session = source
                WHERE live_session IS NULL AND source IS NOT NULL
                ''')

            # Nếu chưa có cột live_session và cũng không có cột source
            if "live_session" not in column_names and "source" not in column_names:
                print("Thêm cột live_session vào bảng price_history")
                cursor.execute("ALTER TABLE price_history ADD COLUMN live_session TEXT")
                print("Đã thêm cột live_session vào bảng price_history")

        # Check for the sync_status table
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='sync_status'")
        if not cursor.fetchone():
            print("Creating sync_status table")
            cursor.execute('''
            CREATE TABLE sync_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                brand_code TEXT UNIQUE,
                sync_status INTEGER DEFAULT 0,
                last_classification TEXT,
                spreadsheet_id TEXT,
                spreadsheet_name TEXT,
                last_sync TIMESTAMP
            )
            ''')
        else:
            # Kiểm tra và thêm cột spreadsheet_name nếu chưa có
            cursor.execute("PRAGMA table_info(sync_status)")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]

            if "spreadsheet_name" not in column_names:
                print("Adding spreadsheet_name column to sync_status table")
                cursor.execute("ALTER TABLE sync_status ADD COLUMN spreadsheet_name TEXT")

        conn.commit()
        conn.close()
    except Exception as e:
        print(f"Error updating database structure: {e}")

# Để đảm bảo cập nhật được áp dụng ngay, gọi hàm check_and_update_table_structure
try:
    check_and_update_table_structure()
except Exception as e:
    print(f"Lỗi khi kiểm tra cấu trúc database: {e}")

# Function to save price threshold
def save_price_threshold(value):
    """Save the price threshold to settings"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        cursor.execute(
            "INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)",
            ("price_threshold", str(value))
        )
        conn.commit()
        conn.close()
    except Exception as e:
        print(f"Error saving price threshold: {e}")

# Function to check if a product belongs to a luxury brand
def is_luxury_brand(product_name):
    """Check if a product belongs to a luxury brand"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()

        # Get all luxury brands
        cursor.execute("SELECT brand_name FROM high_end_brands WHERE is_active = 1")
        luxury_brands = [row[0].lower() for row in cursor.fetchall()]
        conn.close()

        # Check if product name contains any luxury brand
        product_lower = product_name.lower()
        for brand in luxury_brands:
            if brand.lower() in product_lower:
                return True

        return False
    except Exception as e:
        print(f"Error checking luxury brand: {e}")
        return False

# Thêm cache cho phân tích combo
combo_analysis_cache = {}

# Thêm hàm phân tích combo/set
def analyze_combo_set(product_name, model="gpt-4o-mini"):
    """
    Phân tích tên sản phẩm combo/bộ để xác định số lượng sản phẩm trong bộ
    Trả về số lượng sản phẩm dự đoán
    """
    global api_call_count, total_tokens_used

    # Kiểm tra mẫu chuỗi có chứa từ khóa combo hoặc set hay không
    product_lower = product_name.lower()

    # Detect patterns with regex
    # Pattern for "combo x2", "combo 2", "bộ 2", "set 2", "set of 2", "2 món", "2-piece", etc.
    number_pattern = r"(?:combo|set|bộ)(?:\s+x|\s+of\s+|\s+|-)(\d+)"
    pieces_pattern = r"(\d+)(?:\s+|-)(?:món|piece|miếng|items|sp|sản phẩm)"
    in_pattern = r"(\d+)(?:\s*|-)in(?:\s*|-)1"

    # Check for common patterns first
    for pattern in [number_pattern, pieces_pattern, in_pattern]:
        matches = re.findall(pattern, product_lower)
        if matches:
            try:
                return int(matches[0])
            except:
                pass

    # Check for "bộ đôi" = 2
    if any(term in product_lower for term in ["bộ đôi", "combo đôi", "set đôi"]):
        return 2

    # Check for "bộ 3" without space
    if any(term in product_lower for term in ["bộ3", "combo3", "set3"]):
        return 3

    # If none of the patterns matched, but it contains combo/set/bộ keywords, use API
    if any(term in product_lower for term in ["combo", "set", "bộ"]) and model:
        prompt = (
            f"Phân tích tên sản phẩm sau và xác định có BAO NHIÊU SẢN PHẨM trong bộ: \"{product_name}\"\n"
            f"Chỉ trả về số lượng dưới dạng số nguyên (ví dụ: 2, 3, 4...).\n"
            f"Nếu không xác định được, trả về 1.\n"
        )

        try:
            # Sử dụng phiên bản API mới để gọi chat completions
            response = openai_client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=10
            )
            try:
                if hasattr(response, 'usage') and response.usage is not None:
                    total_tokens_used += response.usage.total_tokens
            except Exception:
                pass

            result = response.choices[0].message.content.strip()
            api_call_count += 1
            time.sleep(API_CALL_DELAY)

            # Lấy số từ kết quả
            try:
                count = int(re.search(r"\d+", result).group())
                if 1 <= count <= 10:  # Giới hạn hợp lý
                    return count
            except:
                pass
        except:
            pass

    # Fallback: có combo nhưng không xác định được số lượng
    if any(term in product_lower for term in ["combo", "set", "bộ", "kit"]):
        return 2
    return 1

# Sửa đổi hàm extract_product_category để sử dụng analyze_combo_set
def extract_product_category(product_info, price=0, prompt_template=PROMPT_TEMPLATE, model="gpt-4o-mini"):
    """
    Phân loại sản phẩm bằng cách gọi API OpenAI để phân tích.

    Returns: tuple (classification_result, prompt, extracted_data)
    - classification_result: kết quả phân loại cuối cùng
    - prompt: nội dung prompt đã sử dụng
    - extracted_data: dữ liệu trích xuất thêm từ sản phẩm (nếu có)
    """
    global api_call_count, total_tokens_used

    # Kiểm tra cơ sở dữ liệu nếu sản phẩm đã được phân loại trước đó
    similar_product = find_similar_product(product_info)
    if similar_product:
        print(f"Using similar product match: '{product_info}' -> '{similar_product[0]}' (Score: {similar_product[1]:.2f})")
        # Lấy thông tin phân loại từ DB
        try:
            conn = sqlite3.connect(str(DB_PATH))
            cursor = conn.cursor()

            # Kiểm tra bảng product_classifications có tồn tại không
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='product_classifications'")
            if not cursor.fetchone():
                # Tự động tạo bảng nếu chưa tồn tại
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS product_classifications (
                    product_name TEXT,
                    classification TEXT,
                    brand_code TEXT,
                    price INTEGER DEFAULT 0,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (product_name)
                )
                ''')
                conn.commit()
                # Không có dữ liệu trong bảng mới tạo
                conn.close()
                return None, "", None

            # Thực hiện truy vấn
            cursor.execute("SELECT classification, brand_code FROM product_classifications WHERE product_name = ?",
                        (similar_product[0],))
            result = cursor.fetchone()
            conn.close()

            if result:
                # Trả về kết quả phân loại và không có dữ liệu trích xuất
                return result[0], "", None
        except Exception as e:
            print(f"Error querying database: {e}")
            # Đảm bảo kết nối được đóng
            try:
                conn.close()
            except:
                pass

    # Phân tích số lượng sản phẩm nếu là combo/set (không dùng làm kết quả phân loại)
    combo_quantity = 1
    if any(term in product_info.lower() for term in ['bộ', 'combo', 'set']):
        combo_quantity = analyze_combo_set(product_info, model)

    # Xử lý prompt
    product_description = product_info
    price_text = ""
    if price > 0:
        price_text = f"Giá sản phẩm: {format_price_vnd(price)} VND\n"

    # Chuẩn bị prompt
    prompt = prompt_template.format(product_description=product_description, price_info=price_text)

    # Số lần thử lại khi gặp lỗi API
    max_retries = 3
    retry_delay = 2  # Thời gian chờ giữa các lần thử lại (giây)
    retry_count = 0

    while retry_count < max_retries:
        try:
            # Gọi API OpenAI - sử dụng cú pháp mới của OpenAI API
            response = openai_client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": "Bạn là một trợ lý AI chuyên phân loại sản phẩm trong lĩnh vực thời trang, mỹ phẩm và hàng tiêu dùng."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=500
            )

            # Tính token đã sử dụng
            api_call_count += 1
            try:
                if hasattr(response, 'usage') and response.usage is not None:
                    total_tokens_used += response.usage.total_tokens
            except Exception as e:
                print(f"Lỗi tính token: {str(e)}")

            # Lấy kết quả - cú pháp mới
            result_text = response.choices[0].message.content.strip()

            # Xử lý kết quả từ AI
            result_lines = result_text.strip().split('\n')
            classification = None
            extracted_data = {}

            for line in result_lines:
                line = line.strip()
                if not line:
                    continue

                # Tìm dòng bắt đầu bằng "Phân loại:" hoặc "Classification:"
                if line.lower().startswith(("phân loại:", "classification:")):
                    classification = line.split(":", 1)[1].strip()
                    extracted_data["type_of_product"] = classification
                elif line.lower().startswith("brand:"):
                    extracted_data["brand"] = line.split(":", 1)[1].strip()
                elif line.lower().startswith("tên sản phẩm:"):
                    extracted_data["product_name"] = line.split(":", 1)[1].strip()
                elif line.lower().startswith("mã sản phẩm:"):
                    extracted_data["code"] = line.split(":", 1)[1].strip()
                elif line.lower().startswith(("dung tích", "kích thước")):
                    extracted_data["capacity"] = line.split(":", 1)[1].strip()
                elif line.lower().startswith("số lượng:"):
                    try:
                        quantity_str = line.split(":", 1)[1].strip()
                        quantity = int(re.search(r'\d+', quantity_str).group())
                        extracted_data["quantity"] = quantity
                    except:
                        extracted_data["quantity"] = 1

            # Nếu không thể trích xuất phân loại theo định dạng, sử dụng toàn bộ kết quả
            if not classification:
                classification = post_process(result_text)
            else:
                classification = post_process(classification)

            # Thêm thông tin còn thiếu
            extracted_data["product_info"] = product_info
            if "quantity" not in extracted_data:
                extracted_data["quantity"] = combo_quantity  # Sử dụng combo_quantity đã phân tích

            # Lưu vào DB cho các lần tìm kiếm tương đồng sau này
            if similar_product is None:
                try:
                    conn = sqlite3.connect(str(DB_PATH))
                    cursor = conn.cursor()

                    # Kiểm tra và tạo bảng similar_products nếu chưa tồn tại
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='similar_products'")
                    if not cursor.fetchone():
                        cursor.execute('''
                        CREATE TABLE IF NOT EXISTS similar_products (
                            product_name TEXT,
                            similar_to TEXT,
                            classification TEXT,
                            similarity_score REAL,
                            price INTEGER DEFAULT 0,
                            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            PRIMARY KEY (product_name)
                        )
                        ''')

                    # Điều chỉnh các trường khi lưu
                    cursor.execute(
                        "INSERT OR REPLACE INTO similar_products (product_name, similar_to, classification, similarity_score, price) VALUES (?, ?, ?, ?, ?)",
                        (product_info, product_info, classification, 1.0, price)
                    )
                    conn.commit()
                    conn.close()
                except Exception as e:
                    print(f"Error saving to similar_products: {e}")
                    try:
                        conn.close()
                    except:
                        pass

            return classification, prompt, extracted_data

        except Exception as e:
            retry_count += 1
            error_msg = str(e)

            # Ghi log lỗi
            print(f"Error processing product: {product_info}")
            print(f"Error: {error_msg}")

            # Kiểm tra lỗi Cloudflare hoặc giới hạn tần suất
            is_rate_limit = "rate limit" in error_msg.lower() or "rate_limit" in error_msg.lower()
            is_cloudflare = "520" in error_msg or "cloudflare" in error_msg.lower()

            if retry_count < max_retries:
                # Tăng thời gian chờ nếu là lỗi giới hạn tần suất
                wait_time = retry_delay * (2 ** (retry_count - 1))  # Exponential backoff
                if is_rate_limit:
                    wait_time *= 2  # Chờ lâu hơn cho rate limit

                print(f"Retrying in {wait_time} seconds... (Attempt {retry_count}/{max_retries})")
                time.sleep(wait_time)
            else:
                # Nếu thử lại hết số lần mà vẫn lỗi
                if is_rate_limit:
                    # Nếu là lỗi rate limit, đề xuất chuyển sang model khác
                    if model != "gpt-3.5-turbo":
                        print("Rate limit exceeded, trying fallback model...")
                        return extract_product_category(product_info, price, prompt_template, "gpt-3.5-turbo")
                elif is_cloudflare:
                    # Lỗi từ Cloudflare, thông báo lỗi kết nối
                    print("Connection error from Cloudflare. Network issue or OpenAI service unavailable.")
                    return "Unknown", prompt, {"error": "Cloudflare connection error", "product_info": product_info}

                # Trả về phân loại dự phòng và lưu lỗi
                fallback_classification = "Unknown"
                return fallback_classification, prompt, {"error": error_msg, "product_info": product_info}

def parse_spreadsheet_id(url: str) -> str:
    """Parse spreadsheet ID from URL or return the ID if directly provided"""
    # Check if the input is already a spreadsheet ID format
    if re.match(r'^[a-zA-Z0-9-_]+$', url) and len(url) > 20:
        return url

    # Try to extract from standard Google Sheets URL
    match = re.search(r"/d/([a-zA-Z0-9-_]+)", url)
    if (match):
        return match.group(1)

    # Try to extract from alternate URL formats
    match = re.search(r"spreadsheets/d/([a-zA-Z0-9-_]+)", url)
    if match:
        return match.group(1)

    return url  # Return the input as is if no pattern matches

def extract_date_from_sheet_name(sheet_name):
    """Trích xuất thông tin ngày tháng từ tên sheet để lấy thông tin phiên live"""
    # Mẫu kiểm tra các dạng:
    # - "d.m Internal" hoặc "dd.mm Internal"
    # - "[d.m] Internal" hoặc "[dd.mm] Internal"
    patterns = [
        r'(\d{1,2})\.(\d{1,2}).*Internal',    # d.m hoặc dd.mm theo sau là từ Internal
        r'\[(\d{1,2})\.(\d{1,2})\].*Internal'  # [d.m] hoặc [dd.mm] theo sau là từ Internal
    ]

    for pattern in patterns:
        match = re.search(pattern, sheet_name)
        if match:
            # Lấy ngày và tháng dưới dạng số (không cần dẫn 0)
            day = int(match.group(1))
            month = int(match.group(2))
            # Trả về định dạng d.m
            return f"{day}.{month}"

    # Nếu không có mẫu phù hợp, trả về ngày hiện tại dưới dạng d.m
    current_date = datetime.now()
    return f"{current_date.day}.{current_date.month}"

def extract_live_session_from_spreadsheet(spreadsheet):
    """Trích xuất thông tin phiên live từ tên của Spreadsheet

    Args:
        spreadsheet: Đối tượng Google Spreadsheet đã load

    Returns:
        tuple: (phiên live, tên đầy đủ của spreadsheet)
    """
    # Lấy tên đầy đủ của spreadsheet
    full_name = ""
    try:
        # Nếu spreadsheet có thuộc tính title
        if hasattr(spreadsheet, 'title'):
            full_name = spreadsheet.title
        # Hoặc nếu có thuộc tính properties và title
        elif hasattr(spreadsheet, 'properties') and hasattr(spreadsheet.properties, 'title'):
            full_name = spreadsheet.properties.title
        # Thử lấy thông qua dunder method
        elif hasattr(spreadsheet, '_properties') and 'title' in spreadsheet._properties:
            full_name = spreadsheet._properties['title']
        else:
            # Nếu không thể lấy tên từ các thuộc tính chuẩn, thử gọi API
            try:
                # Có thể cần phải gọi API lấy metadata
                full_name = spreadsheet.fetch_sheet_metadata().get('properties', {}).get('title', '')
            except:
                pass
    except Exception as e:
        print(f"Lỗi khi lấy tên spreadsheet: {str(e)}")

    # Kiểm tra xem còn cách nào khác để lấy tên
    if not full_name and hasattr(spreadsheet, 'get_title'):
        try:
            full_name = spreadsheet.get_title()
        except:
            pass

    # Nếu không lấy được tên, trả về ngày hiện tại
    if not full_name:
        current_date = datetime.now()
        return f"{current_date.day}.{current_date.month}", "Unknown Spreadsheet"

    # Trước tiên, tìm phiên từ đầu tên trước "Internal" - ưu tiên cách này
    # Mẫu để tìm các chữ số ở đầu tên sheet trước từ "Internal"
    pattern_internal = r'^([0-9]+(?:\.[0-9]+)?)\s*(?:Internal|Int)'
    match_internal = re.search(pattern_internal, full_name)
    if match_internal:
        return match_internal.group(1), full_name

    # Tìm mẫu dạng số ở đầu chuỗi, ví dụ "3.3" trong "3.3 Internal | SPE x DL Deal List"
    pattern_start = r'^([0-9]+(?:\.[0-9]+)?)'
    match_start = re.search(pattern_start, full_name)
    if match_start:
        return match_start.group(1), full_name

    # Nếu không tìm thấy ở đầu, thử các mẫu khác
    # Các mẫu phổ biến để trích xuất thông tin phiên live từ tên spreadsheet
    live_patterns = [
        r'\[([0-9]+(?:\.[0-9]+)?)\]',                # Tìm dạng [3.3]
        r'Live\s+([0-9]+(?:\.[0-9]+)?)',             # Tìm dạng "Live 3.3"
        r'Phiên\s+([0-9]+(?:\.[0-9]+)?)',            # Tìm dạng "Phiên 3.3"
        r'Live\s+ngày\s+([0-9]+(?:\.[0-9]+)?)',      # Tìm dạng "Live ngày 3.3"
        r'Deal\s+list\s+([0-9]+(?:\.[0-9]+)?)',      # Tìm dạng "Deal list 3.3"
        r'([0-9]+(?:\.[0-9]+)?)\s+Deal',             # Tìm dạng "3.3 Deal"
        r'([0-9]+(?:\.[0-9]+)?)\s+Internal',         # Tìm dạng "3.3 Internal"
    ]

    for pattern in live_patterns:
        match = re.search(pattern, full_name)
        if match:
            return match.group(1), full_name

    # Nếu không tìm thấy thông tin phiên live, trả về giá trị mặc định
    return "Unknown", full_name

def save_product_classification(product_name, classification, brand_code, price=0, sheet_name="", spreadsheet_id="", live_session=None):
    """Save individual product classification to database with price information"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()

        # Bắt đầu transaction
        cursor.execute("BEGIN TRANSACTION")

        # Kiểm tra xem brand_code đã tồn tại trong bảng brands chưa
        cursor.execute("SELECT COUNT(*) FROM brands WHERE brand_code = ?", (brand_code,))
        brand_exists = cursor.fetchone()[0] > 0

        # Nếu brand_code chưa tồn tại, thêm vào bảng brands
        if not brand_exists:
            # Thử trích xuất tên brand từ tên sản phẩm nếu không có thông tin khác
            brand_name = brand_code  # Mặc định sử dụng brand_code làm tên

            cursor.execute(
                "INSERT INTO brands (brand_code, brand_name, classification) VALUES (?, ?, ?)",
                (brand_code, brand_name, classification)
            )
            print(f"Đã tự động thêm brand mới: {brand_code}")

        # Lưu classification
        cursor.execute(
            "INSERT OR REPLACE INTO product_classifications (product_name, classification, brand_code, price) VALUES (?, ?, ?, ?)",
            (product_name, classification, brand_code, price)
        )

        # Lưu lịch sử giá nếu giá > 0
        if price > 0:
            # Sử dụng live_session đã được truyền vào nếu có
            if not live_session:
                # Nếu live_session không được cung cấp, sử dụng ngày hiện tại
                current_date = datetime.now()
                live_session = f"{current_date.day}.{current_date.month}"

            cursor.execute(
                "INSERT INTO price_history (product_name, price, live_session) VALUES (?, ?, ?)",
                (product_name, price, live_session)
            )

        # Cập nhật trạng thái đồng bộ cho brand_code
        try:
            # Kiểm tra xem brand_code đã có trong bảng sync_status chưa
            cursor.execute("SELECT sync_status FROM sync_status WHERE brand_code = ?", (brand_code,))
            result = cursor.fetchone()

            if result is not None:
                # Cập nhật trạng thái thành "cần đồng bộ" (0)
                cursor.execute(
                    "UPDATE sync_status SET sync_status = 0, last_classification = ? WHERE brand_code = ?",
                    (classification, brand_code)
                )
            else:
                # Thêm mới với trạng thái "cần đồng bộ" (0)
                cursor.execute(
                    "INSERT INTO sync_status (brand_code, sync_status, last_classification, spreadsheet_id) VALUES (?, 0, ?, ?)",
                    (brand_code, classification, spreadsheet_id)
                )
        except Exception as e:
            print(f"Lỗi khi cập nhật trạng thái đồng bộ: {e}")

        # Commit transaction
        conn.commit()
        conn.close()
    except Exception as e:
        print(f"Error saving product classification: {e}")

def get_product_classifications(brand_code):
    """Get all product classifications for a brand code"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        cursor.execute(
            "SELECT product_name, classification FROM product_classifications WHERE brand_code = ?",
            (brand_code,)
        )
        products = cursor.fetchall()
        conn.close()
        return products
    except Exception as e:
        print(f"Error getting product classifications: {e}")
        return []

def get_price_history(product_name):
    """Lấy lịch sử giá của sản phẩm theo thời gian"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()

        # Lấy lịch sử giá sắp xếp theo thời gian
        cursor.execute(
            "SELECT price, timestamp, live_session FROM price_history WHERE product_name = ? ORDER BY timestamp",
            (product_name,)
        )
        price_history = cursor.fetchall()

        # Lấy giá hiện tại từ bảng product_classifications
        cursor.execute(
            "SELECT price FROM product_classifications WHERE product_name = ?",
            (product_name,)
        )
        current_price_result = cursor.fetchone()
        current_price = current_price_result[0] if current_price_result else 0

        conn.close()

        return price_history, current_price
    except Exception as e:
        print(f"Error getting price history: {e}")
        return [], 0

def update_product_price(product_name, new_price, price_date=None):
    """Cập nhật giá mới cho sản phẩm"""
    try:
        # Kiểm tra giá trị hợp lý (dưới 100 triệu)
        MAX_REASONABLE_PRICE = 100000000  # 100 triệu
        if new_price > MAX_REASONABLE_PRICE:
            # Có thể là lỗi định dạng, thử chia cho 1000
            if new_price % 1000 == 0:
                print(f"Giá quá cao ({new_price}), tự động chia cho 1000 -> {new_price//1000}")
                new_price = new_price // 1000
            else:
                # Nếu không thể chia đều cho 1000, có thể là lỗi nhập liệu
                print(f"Cảnh báo: Giá nhập vào ({new_price}) vượt quá 100 triệu đồng")

        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()

        # Bắt đầu transaction
        cursor.execute("BEGIN TRANSACTION")

        # Cập nhật giá hiện tại
        cursor.execute(
            "UPDATE product_classifications SET price = ? WHERE product_name = ?",
            (new_price, product_name)
        )

        # Thêm vào lịch sử giá
        if price_date is None:
            price_date = datetime.now().strftime("%d.%m.%Y")

        cursor.execute(
            "INSERT INTO price_history (product_name, price, price_date, source) VALUES (?, ?, ?, ?)",
            (product_name, new_price, price_date, "Manual Update")
        )

        conn.commit()
        conn.close()
        return True
    except Exception as e:
        print(f"Error updating product price: {e}")
        return False

def auto_update_prices(spreadsheet, deal_sheet_name=None, deal_product_col=None,
                       deal_price_col=None, log_callback=None, progress_callback=None,
                       live_session=None):
    """Tự động cập nhật giá từ Deal list trong Google Spreadsheet

    Args:
        spreadsheet: Đối tượng Google Spreadsheet
        deal_sheet_name: Tên sheet chứa Deal list
        deal_product_col: Tên cột chứa tên sản phẩm
        deal_price_col: Tên cột chứa giá sản phẩm
        log_callback: Hàm callback để ghi log
        progress_callback: Hàm callback để cập nhật tiến trình
        live_session: Phiên Live dạng d.m (ví dụ: "15.6")

    Returns:
        dict: Kết quả cập nhật
    """
    try:
        # Hàm ghi log nếu được cung cấp
        def log(message):
            if log_callback:
                log_callback(message)
            else:
                print(message)

        log("Bắt đầu quét tự động cập nhật giá...")

        # Đảm bảo spreadsheet và deal_sheet_name hợp lệ
        if not spreadsheet:
            log("Lỗi: Spreadsheet chưa được tải")
            return {"error": "Spreadsheet chưa được tải"}

        # Nếu live_session không được cung cấp, trích xuất từ tên spreadsheet
        if not live_session:
            live_session, spreadsheet_name = extract_live_session_from_spreadsheet(spreadsheet)
            log(f"Đang xử lý phiên live: {live_session} (Từ spreadsheet: {spreadsheet_name})")
        else:
            log(f"Đang xử lý phiên live: {live_session} (Do người dùng chọn)")
            # Lấy tên spreadsheet nếu cần
            spreadsheet_name = ""
            if hasattr(spreadsheet, 'title'):
                spreadsheet_name = spreadsheet.title

        # Lấy spreadsheet_id
        spreadsheet_id = ""
        if hasattr(spreadsheet, 'id'):
            spreadsheet_id = spreadsheet.id

        # Thiết lập giá trị mặc định nếu không được cung cấp
        if not deal_sheet_name:
            deal_sheet_name = "Deal list"  # Tên mặc định của sheet chứa Deal list

        if not deal_product_col:
            deal_product_col = "H"  # Cột mặc định chứa tên sản phẩm

        if not deal_price_col:
            deal_price_col = "AB"  # Cột mặc định chứa giá

        # Truy cập Deal list
        try:
            deal_sheet = spreadsheet.worksheet(deal_sheet_name)
            log(f"Đã truy cập worksheet: {deal_sheet_name}")
        except Exception as e:
            log(f"Không tìm thấy sheet '{deal_sheet_name}', đang tìm kiếm thay thế...")

            # Tìm sheet có tên tương tự
            sheet_list = spreadsheet.worksheets()
            similar_sheets = []

            for sheet in sheet_list:
                sheet_name = sheet.title.lower()
                if "deal" in sheet_name or "giá" in sheet_name or "price" in sheet_name:
                    similar_sheets.append(sheet.title)

            if similar_sheets:
                deal_sheet_name = similar_sheets[0]
                log(f"Đã tìm thấy sheet thay thế: {deal_sheet_name}")
                deal_sheet = spreadsheet.worksheet(deal_sheet_name)
            else:
                log(f"Lỗi: Không tìm thấy sheet Deal list phù hợp")
                return {"error": f"Không tìm thấy sheet Deal list phù hợp"}

        # Lấy tất cả dữ liệu từ Deal list
        deal_all = deal_sheet.get_all_values()
        if len(deal_all) < 4:
            log("Không có dữ liệu trong Deal list.")
            return {"error": "Không có dữ liệu trong Deal list."}

        deal_data = deal_all[3:]  # Bỏ 3 dòng tiêu đề
        log(f"Đã tìm thấy {len(deal_data)} dòng dữ liệu trong Deal list")

        # Chuyển đổi tên cột thành chỉ số
        def excel_column_to_index(column_name):
            if not column_name:
                return -1

            column_name = column_name.upper()
            result = 0
            for c in column_name:
                result = result * 26 + (ord(c) - ord('A') + 1)
            return result - 1

        idx_product = excel_column_to_index(deal_product_col)
        idx_price = excel_column_to_index(deal_price_col)

        # Kiểm tra chỉ số hợp lệ
        if idx_product < 0 or idx_price < 0:
            log(f"Lỗi: Chỉ số cột không hợp lệ: Product={idx_product}, Price={idx_price}")
            return {"error": f"Tên cột không hợp lệ: Product={deal_product_col}, Price={deal_price_col}"}

        # Kết nối database
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()

        # Lấy tất cả sản phẩm từ database để so sánh
        cursor.execute("SELECT product_name, price FROM product_classifications")
        db_products = {row[0]: row[1] for row in cursor.fetchall()}

        log(f"Đã tìm thấy {len(db_products)} sản phẩm trong database")

        # Bắt đầu quét và cập nhật
        updated_products = []
        scanned_count = 0
        updated_count = 0

        for i, row in enumerate(deal_data):
            # Báo cáo tiến độ mỗi 10%
            if i % max(1, len(deal_data) // 10) == 0 and progress_callback:
                progress = min(99, int(i / len(deal_data) * 100))
                progress_callback(progress)

            try:
                # Kiểm tra độ dài của row để tránh lỗi index
                if not row or len(row) <= max(idx_product, idx_price):
                    continue


                # Trích xuất dữ liệu
                product_name = row[idx_product].strip()
                if not product_name:
                    continue

                # Thêm kiểm tra cột N - Pick (chỉ xử lý nếu có "Yes")
                idx_pick = 11  # Cột N (0-based index)
                pick_value = row[idx_pick].strip() if len(row) > idx_pick else ""

                if pick_value.lower() != "yes":
                    continue

                # Lấy giá từ Deal list
                price_text = row[idx_price].strip() if len(row) > idx_price else ""
                if not price_text:
                    continue

                # Chuyển đổi giá
                cleaned_price = re.sub(r'[^\d]', '', price_text)
                if not cleaned_price:
                    continue

                sheet_price = int(cleaned_price)

                # Kiểm tra giá trị hợp lý (dưới 100 triệu)
                MAX_REASONABLE_PRICE = 100000000  # 100 triệu
                if sheet_price > MAX_REASONABLE_PRICE:
                    # Có thể là lỗi định dạng, thử chia cho 1000
                    if sheet_price % 1000 == 0:
                        log(f"Giá quá cao cho '{product_name}' ({sheet_price}), tự động chia cho 1000 -> {sheet_price//1000}")
                        sheet_price = sheet_price // 1000

                scanned_count += 1

                # Kiểm tra nếu sản phẩm có trong database
                if product_name in db_products:
                    db_price = db_products[product_name]

                    # So sánh giá (cho phép sai số 5% để tránh cập nhật không cần thiết)
                    price_diff_percent = abs(sheet_price - db_price) / max(1, db_price) * 100

                    # Nếu giá thay đổi > 5% hoặc > 10,000đ
                    if (price_diff_percent > 5 or abs(sheet_price - db_price) > 10000) and sheet_price > 0:
                        # Sử dụng thông tin phiên live thay vì ngày hiện tại
                        price_date = live_session

                        cursor.execute("BEGIN TRANSACTION")

                        # Cập nhật giá hiện tại
                        cursor.execute(
                            "UPDATE product_classifications SET price = ? WHERE product_name = ?",
                            (sheet_price, product_name)
                        )

                        # Tạo nguồn với thông tin đầy đủ
                        source = f"{deal_sheet_name} - {spreadsheet_name}"
                        if spreadsheet_id:
                            short_id = spreadsheet_id[-8:] if len(spreadsheet_id) > 8 else spreadsheet_id
                            source = f"{deal_sheet_name} ({short_id}) - {spreadsheet_name}"

                        # Thêm vào lịch sử giá với thông tin phiên live
                        cursor.execute(
                            "INSERT INTO price_history (product_name, price, price_date, source) VALUES (?, ?, ?, ?)",
                            (product_name, sheet_price, price_date, source)
                        )

                        cursor.execute("COMMIT")

                        updated_count += 1
                        updated_products.append({
                            "name": product_name,
                            "old_price": db_price,
                            "new_price": sheet_price,
                            "change_percent": price_diff_percent,
                            "live_session": price_date
                        })

                        log(f"Đã cập nhật giá cho '{product_name}': {format_price_vnd(db_price)}đ -> {format_price_vnd(sheet_price)}đ ({price_diff_percent:.1f}%) [Phiên live: {price_date}]")
            except Exception as e:
                log(f"Lỗi xử lý dòng {i+3} trong Deal list: {str(e)}")
                continue

        # Hoàn tất và báo cáo
        conn.close()

        if progress_callback:
            progress_callback(100)

        log(f"Hoàn tất cập nhật giá tự động. Đã quét {scanned_count} sản phẩm, cập nhật {updated_count} sản phẩm.")

        return {
            "scanned": scanned_count,
            "updated": updated_count,
            "products": updated_products,
            "live_session": live_session,
            "spreadsheet_name": spreadsheet_name
        }
    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        if log_callback:
            log_callback(f"Lỗi khi tự động cập nhật giá: {str(e)}")
            log_callback(f"Chi tiết lỗi: {error_traceback}")
        return {"error": str(e), "detail": error_traceback}

class ProcessingWorker(QObject):
    log_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(object)
    # Add new signal for real-time updates
    item_processed_signal = pyqtSignal(str, str, list)  # brand_code, classification, product_details

    def __init__(self, spreadsheet, brand_sheet_name, deal_sheet_name,
                 brand_code_col, brand_type_col, deal_brand_code_col, deal_product_col,
                 last_row, model, brand_name_col="D", deal_price_col="AB", deal_pick_col="N"):
        super().__init__()
        self.spreadsheet = spreadsheet
        self.brand_sheet_name = brand_sheet_name
        self.deal_sheet_name = deal_sheet_name
        self.brand_code_col = brand_code_col
        self.brand_type_col = brand_type_col
        self.brand_name_col = brand_name_col
        self.deal_brand_code_col = deal_brand_code_col
        self.deal_product_col = deal_product_col
        self.deal_price_col = deal_price_col  # Thêm tham số cột giá
        self.deal_pick_col = deal_pick_col    # Thêm tham số cột pick
        self.last_row = last_row  # Số dòng cuối cùng cần xử lý
        self.model = model
        self.live_session = None  # Thêm thuộc tính live_session

    def run(self):
        brand_feedback = {}
        brand_products = {}  # Store product names for each brand code
        brand_products_detail = {}  # Store detailed product data (name, classification)

        try:
            brand_sheet = self.spreadsheet.worksheet(self.brand_sheet_name)
            deal_sheet = self.spreadsheet.worksheet(self.deal_sheet_name)
        except Exception as e:
            self.log_signal.emit(f"Error accessing sheets: {e}")
            self.finished_signal.emit(brand_feedback)
            return

        brand_all = brand_sheet.get_all_values()
        if len(brand_all) < 2:
            self.log_signal.emit("Không có dữ liệu trong Brand list.")
            self.finished_signal.emit(brand_feedback)
            return
        # Xử lý đến dòng được chỉ định (nếu nhập)
        try:
            last = int(self.last_row)
            brand_data = brand_all[1:last]  # Bỏ tiêu đề
        except Exception:
            brand_data = brand_all[1:]
        deal_all = deal_sheet.get_all_values()
        if len(deal_all) < 4:
            self.log_signal.emit("Không có dữ liệu trong Deal list.")
            self.finished_signal.emit(brand_feedback)
            return
        deal_data = deal_all[3:]  # Bỏ 3 dòng tiêu đề

        # Hàm chuyển đổi tên cột thành chỉ số
        def excel_column_to_index(column_name):
            if not column_name:
                return -1

            column_name = column_name.upper()
            result = 0
            for c in column_name:
                result = result * 26 + (ord(c) - ord('A') + 1)
            return result - 1

        # Tạo mapping: Brand code -> danh sách sản phẩm
        deal_mapping = {}
        for row in deal_data:
            try:
                idx_brand = excel_column_to_index(self.deal_brand_code_col)
                idx_product = excel_column_to_index(self.deal_product_col)
                idx_price = excel_column_to_index(self.deal_price_col)  # Tính index của cột giá

                brand_code = row[idx_brand].strip()
                product_name = row[idx_product].strip()

                # Đọc giá sản phẩm nếu có
                price_value = 0
                if len(row) > idx_price:
                    try:
                        # Làm sạch giá (loại bỏ ký tự không phải số)
                        price_text = row[idx_price].strip()
                        cleaned_price = re.sub(r'[^\d]', '', price_text)
                        if cleaned_price:
                            price_value = int(cleaned_price)

                    except Exception as e:
                        self.log_signal.emit(f"Lỗi xử lý giá cho sản phẩm '{product_name}': {e}")
                        price_value = 0

                # Thêm kiểm tra cột N - Pick (chỉ xử lý nếu có "Yes")
                idx_pick = excel_column_to_index(self.deal_pick_col)  # Sử dụng cột pick từ input
                idx_review = idx_pick + 1  # Cột Review luôn nằm kế bên cột Pick

                pick_value = row[idx_pick].strip() if len(row) > idx_pick else ""
                review_value = row[idx_review].strip() if len(row) > idx_review else ""

                if pick_value.lower() != "yes":
                    continue

                has_review = bool(review_value)

                if brand_code:
                    if brand_code not in deal_mapping:
                        deal_mapping[brand_code] = []
                    deal_mapping[brand_code].append({
                        "name": product_name,
                        "has_review": has_review,
                        "price": price_value  # Thêm giá vào dữ liệu
                    })
            except Exception as e:
                self.log_signal.emit(f"Lỗi đọc dòng trong Deal list: {e}")
                continue

        updated_count = 0
        for i, row in enumerate(brand_data, start=2):
            try:
                idx_brand = excel_column_to_index(self.brand_code_col)
                idx_type = excel_column_to_index(self.brand_type_col)
                idx_brand_name = excel_column_to_index(self.brand_name_col)
                brand_code = row[idx_brand].strip() if len(row) > idx_brand else ""
                brand_name = row[idx_brand_name].strip() if len(row) > idx_brand_name else ""
            except Exception as e:
                self.log_signal.emit(f"Lỗi đọc dòng {i} trong Brand list: {e}")
                continue

            if not brand_code:
                self.log_signal.emit(f"Dòng {i}: Brand code trống. Dừng xử lý.")
                break

            if brand_code not in deal_mapping:
                # Bỏ qua thông báo này để giảm log
                # self.log_signal.emit(f"Brand code '{brand_code}': Không tìm thấy sản phẩm trong Deal list.")
                continue

            product_items = deal_mapping[brand_code]
            # Bỏ qua log số lượng sản phẩm để giảm log
            # self.log_signal.emit(f"Đang phân loại {len(product_items)} sản phẩm cho brand {brand_code}...")
            classifications_with_priority = []
            product_names = []  # Track all product names for this brand
            product_details = []  # Store product name and its classification

            for product_item in product_items:
                product = product_item["name"]
                has_review = product_item["has_review"]
                price = product_item.get("price", 0)  # Lấy giá sản phẩm
                product_names.append(product)  # Save product name for reference

                # Gọi hàm extract_product_category với giá
                result, _, extracted_data = extract_product_category(product, price=price, model=self.model)
                if result:
                    classifications_with_priority.append({
                        "category": result,
                        "has_review": has_review
                    })
                    # Save the product and its classification
                    product_details.append((product, result))
                    # Store in database for future reference with price
                    # Truyền giá trị live_session vào hàm save_product_classification
                    save_product_classification(product, result, brand_code, price, self.deal_sheet_name,
                                              getattr(self.spreadsheet, 'id', ''), self.live_session)

                    # Store product details in database if we have extracted data
                    if extracted_data and isinstance(extracted_data, dict):
                        # Thêm thông tin sản phẩm và brand name vào extracted_data để lưu
                        extracted_data["product_info"] = product

                        # Nếu brand name từ sheet khác rỗng và khác với brand đã trích xuất, ưu tiên sử dụng
                        if brand_name and (not extracted_data.get("brand") or brand_name.lower() != extracted_data.get("brand").lower()):
                            extracted_data["brand"] = brand_name.lower()

                        # Lưu thông tin đầy đủ vào database
                        save_product_details(extracted_data)

            # Sắp xếp theo ưu tiên: đầu tiên là các sản phẩm có review, sau đó là các sản phẩm không có review
            classifications_with_priority.sort(key=lambda x: 0 if x["has_review"] else 1)

            # Lấy danh sách các loại sản phẩm duy nhất nhưng vẫn giữ thứ tự ưu tiên
            seen = set()
            unique_classifications = []
            for item in classifications_with_priority:
                category = item["category"]
                if category not in seen:
                    seen.add(category)
                    unique_classifications.append(category)

            # Kiểm tra nếu không có phân loại nào
            if not unique_classifications:
                self.log_signal.emit(f"Brand code '{brand_code}': Không có phân loại nào được tìm thấy. Bỏ qua cập nhật.")
                continue

            final_result = ", ".join(unique_classifications)

            # Tạo thông báo chi tiết hơn với từng sản phẩm đã phân loại
            product_summary = ", ".join([f"{product[0]} ({product[1]})" for product in product_details])
            self.log_signal.emit(f"🔍 Brand '{brand_code}' có {len(product_details)} sản phẩm cần phân loại")

            try:
                brand_sheet.update_cell(i, idx_type + 1, final_result)
                updated_count += 1
                brand_feedback[brand_code] = final_result
                brand_products[brand_code] = " | ".join(product_names[:3])  # Lưu tối đa 3 tên sản phẩm
                brand_products_detail[brand_code] = product_details

                # Gửi thông tin chi tiết về UI qua signal
                self.item_processed_signal.emit(brand_code, final_result, product_details)

                # Thêm thông báo hoàn tất phân loại với emoji
                self.log_signal.emit(f"🏷️ Brand '{brand_code}': Đã xử lý {len(product_details)} sản phẩm | Phân loại: {final_result}")
            except Exception as e:
                self.log_signal.emit(f"Lỗi cập nhật dòng {i}: {e}")

        # Pass both the classification results and detailed product data
        result_data = {
            "feedback": brand_feedback,
            "products": brand_products,
            "product_details": brand_products_detail
        }

        self.log_signal.emit(f"Xử lý hoàn tất. Đã cập nhật {updated_count} dòng.")
        self.log_signal.emit(f"Tổng số lần gọi API: {api_call_count}")
        self.log_signal.emit(f"Tổng token đã dùng: {total_tokens_used}")
        self.finished_signal.emit(result_data)

class ProductDetailDialog(QDialog):
    """Dialog for showing and editing product classifications"""

    def __init__(self, brand_code, products_data, parent=None):
        super().__init__(parent)
        self.brand_code = brand_code
        self.products_data = products_data
        self.setWindowTitle(f"Chi tiết sản phẩm - {brand_code}")
        self.setMinimumSize(700, 500)
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()

        # Product table
        self.product_table = QTableWidget()
        self.product_table.setColumnCount(3)
        self.product_table.setHorizontalHeaderLabels(["Tên sản phẩm", "Phân loại", "Lịch sử giá"])
        self.product_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        self.product_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        self.product_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)

        # Kết nối sự kiện double-click để mở biểu đồ giá
        self.product_table.cellDoubleClicked.connect(self.handle_table_double_click)

        # Populate table
        self.product_table.setRowCount(len(self.products_data))
        for i, (product_name, classification) in enumerate(self.products_data):
            name_item = QTableWidgetItem(product_name)
            class_item = QTableWidgetItem(classification)
            class_item.setFlags(class_item.flags() | Qt.ItemFlag.ItemIsEditable)

            # Thêm nút hiển thị lịch sử giá
            history_btn = QPushButton("Xem biểu đồ")
            history_btn.clicked.connect(lambda checked, p=product_name: self.show_price_history(p))

            self.product_table.setItem(i, 0, name_item)
            self.product_table.setItem(i, 1, class_item)
            self.product_table.setCellWidget(i, 2, history_btn)

        layout.addWidget(self.product_table)

        # Buttons
        button_layout = QHBoxLayout()
        save_btn = QPushButton("Lưu thay đổi")
        save_btn.clicked.connect(self.save_changes)
        close_btn = QPushButton("Đóng")
        close_btn.clicked.connect(self.reject)

        button_layout.addWidget(save_btn)
        button_layout.addWidget(close_btn)
        layout.addLayout(button_layout)

        self.setLayout(layout)

    def handle_table_double_click(self, row, column):
        """Xử lý sự kiện double-click vào bảng sản phẩm"""
        # Lấy tên sản phẩm từ ô đầu tiên của dòng được chọn
        product_name = self.product_table.item(row, 0).text()
        # Mở biểu đồ giá
        self.show_price_history(product_name)

    def show_price_history(self, product_name):
        """Hiển thị biểu đồ lịch sử giá cho sản phẩm"""
        dialog = PriceHistoryDialog(product_name, self)
        dialog.exec()

    def save_changes(self):
        """Save changes to product classifications"""
        updated_products = []

        for i in range(self.product_table.rowCount()):
            product_name = self.product_table.item(i, 0).text()
            classification = self.product_table.item(i, 1).text()

            # Save to database
            save_product_classification(product_name, classification, self.brand_code, 0)
            updated_products.append((product_name, classification))

        self.products_data = updated_products
        self.accept()

    def get_updated_data(self):
        """Return the updated product classifications"""
        return self.products_data

class SheetLoaderWorker(QObject):
    """Worker for loading Google Sheets in background thread"""
    finished = pyqtSignal(object)  # Emit the spreadsheet object or None
    error = pyqtSignal(str)        # Emit error message

    def __init__(self, spreadsheet_id, oauth_base64):
        super().__init__()
        self.spreadsheet_id = spreadsheet_id
        self.oauth_base64 = oauth_base64

    def run(self):
        try:
            gs_manager = GoogleSheetManager(auth_type="oauth", credentials_data=self.oauth_base64)
            spreadsheet = gs_manager.open_by_key(self.spreadsheet_id)
            self.finished.emit((gs_manager, spreadsheet))
        except Exception as e:
            self.error.emit(str(e))
            self.finished.emit((None, None))


class LoadingDialog(QDialog):
    """Dialog showing loading animation while loading sheet"""

    def __init__(self, parent=None, message="Đang tải dữ liệu..."):
        super().__init__(parent)
        self.setWindowTitle("Loading")
        self.setFixedSize(350, 80)
        # Xóa nút help, cho phép hide khi click bên ngoài
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowType.WindowContextHelpButtonHint | Qt.WindowType.WindowStaysOnTopHint)
        self.setModal(True)

        # Tự động phát hiện chế độ sáng/tối của hệ thống
        self.detect_system_theme()

        # Setup UI
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # Loading message
        self.message_label = QLabel(message)
        self.message_label.setStyleSheet(f"color: {self.text_color}; font-size: 13px;")
        layout.addWidget(self.message_label)

        # Progress bar - Thiết kế mỏng và hiện đại
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)  # Determinate mode cho animation đẹp hơn
        self.progress_bar.setTextVisible(False)  # Ẩn văn bản phần trăm
        self.progress_bar.setFixedHeight(4)  # Thanh progress mỏng
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                background-color: {self.progress_bg};
                border: none;
                border-radius: 2px;
            }}
            QProgressBar::chunk {{
                background-color: {self.progress_color};
                border-radius: 2px;
            }}
        """)
        layout.addWidget(self.progress_bar)

        # Timer for smooth animation
        self.timer = QTimer(self)
        self.timer.timeout.connect(self._update_progress)
        self.progress_value = 0

    def detect_system_theme(self):
        """Phát hiện chế độ sáng/tối của hệ thống"""
        try:
            from PyQt6.QtGui import QPalette
            palette = self.palette()
            bg_color = palette.color(QPalette.ColorRole.Window)
            is_dark = bg_color.lightness() < 128

            if is_dark:
                # Dark mode
                self.progress_color = "#2196F3"  # Blue
                self.progress_bg = "#424242"     # Dark gray
                self.text_color = "#FFFFFF"      # White
                self.setStyleSheet("background-color: #303030;")
            else:
                # Light mode
                self.progress_color = "#2196F3"  # Blue
                self.progress_bg = "#E0E0E0"     # Light gray
                self.text_color = "#303030"      # Dark gray
                self.setStyleSheet("background-color: #FFFFFF;")
        except:
            # Fallback to light theme if detection fails
            self.progress_color = "#2196F3"
            self.progress_bg = "#E0E0E0"
            self.text_color = "#303030"

    def showEvent(self, event):
        """Start animation when dialog is shown"""
        super().showEvent(event)
        # Reset progress
        self.progress_value = 0
        self.progress_bar.setValue(0)
        self.timer.start(16)  # ~60 FPS for smooth animation

    def closeEvent(self, event):
        """Stop animation when dialog is closed"""
        self.timer.stop()
        super().closeEvent(event)

    def _update_progress(self):
        """Cập nhật progress bar với animation đẹp mắt"""
        # Animation trôi từ trái sang phải
        self.progress_value = (self.progress_value + 1) % 110

        if self.progress_value > 100:
            # Reset về 0 khi đạt 100%
            self.progress_value = 0

        self.progress_bar.setValue(self.progress_value)

class AIClassificationWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)

        # Signals and callbacks
        self.back_callback = None  # Callback để quay lại màn hình trước
        self.goto_callback = None  # Callback để chuyển đến chương trình khác
        self.goto_other_program = lambda prog: self.goto_other_program(prog)  # Ngăn chặn đệ quy

        # Các biến lưu trữ spreadsheet và worker
        self.spreadsheet = None
        self.processing_worker = None
        self.thread_pool = None

        # Biến lưu trữ chi tiết sản phẩm
        self.product_details = {}

        # Biến checkpoint cho tính năng Tạm dừng/Tiếp tục
        self.checkpoint = {
            "active": False,
            "spreadsheet_id": "",
            "brand_sheet_name": "",
            "deal_sheet_name": "",
            "brand_code_col": "",
            "brand_type_col": "",
            "brand_name_col": "",
            "deal_brand_code_col": "",
            "deal_product_col": "",
            "deal_price_col": "",
            "current_row": 0,
            "model": ""
        }

        try:
            # Khởi tạo thư mục dữ liệu nếu chưa tồn tại
            if not DATA_DIR.exists():
                os.makedirs(DATA_DIR, exist_ok=True)

            # Khởi tạo hoặc nâng cấp database
            setup_database()
            check_and_update_table_structure()

            # Set focus policy to prevent focus issues
            self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)
            self.installEventFilter(self)

            # Create ThreadPool
            self.thread_pool = QThreadPool.globalInstance()
            self.thread_pool.setMaxThreadCount(4)  # Giới hạn số thread để tránh quá tải
            print(f"Số lượng thread tối đa: {self.thread_pool.maxThreadCount()}")

            # Setup UI
            self.api_call_count = 0  # Reset counter for this session
            self.setup_ui()
            # auto-load settings/feedback
            self.load_feedback()

            # Kiểm tra nếu có checkpoint
            self.load_checkpoint()
        except Exception as e:
            import traceback
            error_msg = traceback.format_exc()
            print(f"Lỗi khởi tạo: {str(e)}\n{error_msg}")
            QMessageBox.critical(
                self,
                "Lỗi khởi tạo",
                f"Đã xảy ra lỗi khi khởi tạo ứng dụng:\n{str(e)}"
            )

    def setup_ui(self):
        main_layout = QVBoxLayout(self)

        # Create top bar with Back and GoTo buttons
        top_bar = QHBoxLayout()

        # Create properly styled back button
        btn_back = QPushButton("⬅️ Trở về giao diện chính")
        btn_back.setFixedSize(150, 40)
        btn_back.setStyleSheet("border: none; font-size: 12px; font-weight: bold;")
        btn_back.clicked.connect(self.handle_back)
        top_bar.addWidget(btn_back, alignment=Qt.AlignmentFlag.AlignLeft)

        # Thêm nút Quản lý dữ liệu nổi bật
        btn_database = QPushButton("📊 Quản lý dữ liệu")
        btn_database.setFixedSize(150, 40)
        btn_database.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        btn_database.clicked.connect(self.show_database_manager)
        top_bar.addWidget(btn_database, alignment=Qt.AlignmentFlag.AlignCenter)

        # Create GoTo button with dropdown menu
        self.context_button = QToolButton()
        self.context_button.setText("Chuyển đến ➡️")
        self.context_button.setFixedSize(100, 30)
        self.context_button.setStyleSheet("""
            QToolButton {
                border: none;
                background: transparent;
                font-size: 12px;
                font-weight: bold;
            }
            QToolButton::menu-indicator {
                image: none;
                width: 0px;
            }
        """)
        self.context_button.installEventFilter(self)
        self.context_button.setPopupMode(QToolButton.ToolButtonPopupMode.InstantPopup)
        menu = QMenu(self.context_button)
        menu.addAction("Data Handler", lambda: self.goto_other_program("data_handler"))
        menu.addAction("Data Scraping", lambda: self.goto_other_program("autoshopee_scraping"))
        menu.addAction("Create Internal Data", lambda: self.goto_other_program("internal_data"))
        menu.addAction("Import Data", lambda: self.goto_other_program("import_data"))
        menu.addAction("Image Scraping", lambda: self.goto_other_program("image_scraping"))
        menu.addAction("Update Level Model", lambda: self.goto_other_program("external_update"))
        menu.addAction("Basket Arrangement", lambda: self.goto_other_program("basket_arrangement"))
        self.context_button.setMenu(menu)
        top_bar.addWidget(self.context_button, alignment=Qt.AlignmentFlag.AlignRight)

        main_layout.addLayout(top_bar)
        main_layout.addSpacing(10)

        # Create content widget and layout
        content_layout = QVBoxLayout()

        # Layout cho 2 GroupBox dòng 1
        top_row1_layout = QHBoxLayout()

        # 1. Google Sheet Group
        self.group_sheet = QGroupBox("Google Sheet")
        sheet_layout = QVBoxLayout()
        self.sheet_link_input = QLineEdit()
        self.sheet_link_input.setPlaceholderText("Nhập link Google Sheet")
        self.sheet_link_input.textChanged.connect(self.on_sheet_url_changed)
        self.load_sheet_btn = QPushButton("Load Sheet")
        self.load_sheet_btn.clicked.connect(self.load_sheet)
        sheet_layout.addWidget(self.sheet_link_input)
        sheet_layout.addWidget(self.load_sheet_btn)
        self.group_sheet.setLayout(sheet_layout)
        top_row1_layout.addWidget(self.group_sheet)

        # 2. Brand List Group
        self.group_brand = QGroupBox("Brand List")
        brand_layout = QVBoxLayout()

        # Sheet selection
        sheet_row = QHBoxLayout()
        sheet_label = QLabel("Sheet:")
        self.brand_sheet_combo = QComboBox()
        self.brand_sheet_combo.setMinimumWidth(200)
        self.brand_sheet_combo.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        sheet_row.addWidget(sheet_label)
        sheet_row.addWidget(self.brand_sheet_combo)
        brand_layout.addLayout(sheet_row)

        # Brand code and Type columns in one row
        columns_row = QHBoxLayout()
        code_layout = QHBoxLayout()
        code_label = QLabel("Brand Code:")
        self.brand_code_input = QLineEdit("A")
        code_layout.addWidget(code_label)
        code_layout.addWidget(self.brand_code_input)

        type_layout = QHBoxLayout()
        type_label = QLabel("Type:")
        self.brand_type_input = QLineEdit("E")
        type_layout.addWidget(type_label)
        type_layout.addWidget(self.brand_type_input)

        columns_row.addLayout(code_layout)
        columns_row.addLayout(type_layout)
        brand_layout.addLayout(columns_row)

        # Brand name và Dòng cuối cùng nằm trên cùng một dòng
        name_row_layout = QHBoxLayout()

        # Brand name column
        brand_name_layout = QHBoxLayout()
        brand_name_label = QLabel("Brand Name:")
        self.brand_name_input = QLineEdit("D")
        brand_name_label.setToolTip("Cột chứa tên thương hiệu để khớp với brand từ API")
        self.brand_name_input.setToolTip("Cột chứa tên thương hiệu để khớp với brand từ API")
        brand_name_layout.addWidget(brand_name_label)
        brand_name_layout.addWidget(self.brand_name_input)

        # Last row input
        last_row_layout = QHBoxLayout()
        last_row_label = QLabel("Dòng cuối:")
        self.last_row_input = QLineEdit("")
        self.last_row_input.setPlaceholderText("Nhập vị trí dòng cuối cần xử lý")
        last_row_layout.addWidget(last_row_label)
        last_row_layout.addWidget(self.last_row_input)

        # Thêm cả hai layout vào cùng một dòng
        name_row_layout.addLayout(brand_name_layout)
        name_row_layout.addLayout(last_row_layout)
        brand_layout.addLayout(name_row_layout)

        self.group_brand.setLayout(brand_layout)
        top_row1_layout.addWidget(self.group_brand)

        # Thêm dòng 1 vào layout chính
        content_layout.addLayout(top_row1_layout)

        # Layout cho 2 GroupBox dòng 2
        top_row2_layout = QHBoxLayout()

        # 3. Deal List Group
        self.group_deal = QGroupBox("Deal List")
        deal_layout = QVBoxLayout()

        # Sheet selection
        deal_sheet_row = QHBoxLayout()
        deal_sheet_label = QLabel("Sheet:")
        self.deal_sheet_combo = QComboBox()
        self.deal_sheet_combo.setMinimumWidth(200)
        self.deal_sheet_combo.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        deal_sheet_row.addWidget(deal_sheet_label)
        deal_sheet_row.addWidget(self.deal_sheet_combo)
        deal_layout.addLayout(deal_sheet_row)

        # Brand code and Product name columns in one row
        deal_columns_row = QHBoxLayout()
        deal_code_layout = QHBoxLayout()
        deal_code_label = QLabel("Brand Code:")
        self.deal_brand_code_input = QLineEdit("A")
        deal_code_layout.addWidget(deal_code_label)
        deal_code_layout.addWidget(self.deal_brand_code_input)

        deal_product_layout = QHBoxLayout()
        deal_product_label = QLabel("Tên sản phẩm:")
        self.deal_product_input = QLineEdit("X")
        deal_product_layout.addWidget(deal_product_label)
        deal_product_layout.addWidget(self.deal_product_input)

        deal_columns_row.addLayout(deal_code_layout)
        deal_columns_row.addLayout(deal_product_layout)
        deal_layout.addLayout(deal_columns_row)

        # Tạo một dòng cho cả cột giá và cột pick
        price_pick_row = QHBoxLayout()

        # Phần giá sản phẩm
        deal_price_layout = QHBoxLayout()
        deal_price_label = QLabel("Giá sản phẩm:")
        self.deal_price_input = QLineEdit("AB")
        self.deal_price_input.setPlaceholderText("Nhập tên cột chứa giá gốc")
        deal_price_layout.addWidget(deal_price_label)
        deal_price_layout.addWidget(self.deal_price_input)

        # Phần cột Pick
        deal_pick_layout = QHBoxLayout()
        deal_pick_label = QLabel("Cột Pick:")
        self.deal_pick_input = QLineEdit("N")
        self.deal_pick_input.setPlaceholderText("Nhập tên cột Pick")
        deal_pick_layout.addWidget(deal_pick_label)
        deal_pick_layout.addWidget(self.deal_pick_input)

        # Thêm cả hai vào cùng một dòng
        price_pick_row.addLayout(deal_price_layout)
        price_pick_row.addLayout(deal_pick_layout)
        deal_layout.addLayout(price_pick_row)

        self.group_deal.setLayout(deal_layout)
        top_row2_layout.addWidget(self.group_deal)

        # 4. Advanced Settings Group
        self.group_advanced = QGroupBox("Advanced Settings")
        adv_layout = QVBoxLayout()

        # Model selection
        model_row = QHBoxLayout()
        model_label = QLabel("Chọn model:")
        self.model_combo = QComboBox()
        self.model_combo.setMinimumWidth(200)
        self.model_combo.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.model_combo.addItems(["gpt-4o-mini", "gpt-3.5-turbo"])
        model_row.addWidget(model_label)
        model_row.addWidget(self.model_combo)
        adv_layout.addLayout(model_row)

        # Similarity threshold
        threshold_row = QHBoxLayout()
        threshold_label = QLabel("Ngưỡng tương đồng:")
        # Tải ngưỡng tương đồng đã lưu từ database
        saved_threshold = get_similarity_threshold()
        self.similarity_threshold = QLineEdit(str(saved_threshold))
        self.similarity_threshold.setToolTip("Ngưỡng tương đồng 0.0-1.0 (Cao hơn = Chính xác hơn, Thấp hơn = Nhanh hơn)")
        threshold_row.addWidget(threshold_label)
        threshold_row.addWidget(self.similarity_threshold)
        adv_layout.addLayout(threshold_row)

        # Thêm input field cho Phiên Live
        live_session_row = QHBoxLayout()
        live_session_label = QLabel("Phiên Live:")
        self.live_session_date = QDateEdit()
        self.live_session_date.setCalendarPopup(True)
        self.live_session_date.setDate(QDate.currentDate())
        self.live_session_date.setDisplayFormat("dd.MM")
        self.live_session_date.setToolTip("Chọn ngày của phiên live (định dạng ngày.tháng)")
        live_session_row.addWidget(live_session_label)
        live_session_row.addWidget(self.live_session_date)
        adv_layout.addLayout(live_session_row)

        self.group_advanced.setLayout(adv_layout)
        top_row2_layout.addWidget(self.group_advanced)

        # Thêm dòng 2 vào layout chính
        content_layout.addLayout(top_row2_layout)

        # 5. Processing Log Group (Dòng 3)
        log_group = QGroupBox("Processing Log")
        log_layout = QVBoxLayout()
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)

        # Thêm layout cho nút Xử lý dữ liệu và Tạm dừng/Tiếp tục
        process_buttons_layout = QHBoxLayout()

        # Nút Xử lý dữ liệu
        self.process_btn = QPushButton("Xử lý dữ liệu")
        self.process_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.process_btn.setMinimumHeight(25)
        self.process_btn.clicked.connect(self.start_processing)
        process_buttons_layout.addWidget(self.process_btn)

        # Nút Tạm dừng/Tiếp tục
        self.pause_resume_btn = QPushButton("Tạm dừng")
        self.pause_resume_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.pause_resume_btn.setMinimumHeight(25)
        self.pause_resume_btn.clicked.connect(self.toggle_pause_resume)
        self.pause_resume_btn.setEnabled(False)  # Ban đầu vô hiệu hóa nút
        process_buttons_layout.addWidget(self.pause_resume_btn)

        # Thêm layout nút vào log_layout
        log_layout.addLayout(process_buttons_layout)

        log_group.setLayout(log_layout)
        content_layout.addWidget(log_group)

        # 6. Active Learning Group (Dòng 4)
        self.group_feedback = QGroupBox("Active Learning")
        feedback_layout = QVBoxLayout()
        self.feedback_table = QTableWidget()
        self.feedback_table.setColumnCount(3)
        self.feedback_table.setHorizontalHeaderLabels(["Brand Code", "Loại sản phẩm", "Sản phẩm"])
        self.feedback_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        self.feedback_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        self.feedback_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        feedback_layout.addWidget(self.feedback_table)

        # Thêm layout nút cho Lưu Feedback
        button_layout = QHBoxLayout()

        # Style the save_feedback_btn consistently with process_btn
        self.save_feedback_btn = QPushButton("Đồng bộ dữ liệu")
        self.save_feedback_btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.save_feedback_btn.setMinimumHeight(25)  # Set consistent height
        self.save_feedback_btn.clicked.connect(self.save_feedback)
        button_layout.addWidget(self.save_feedback_btn)

        feedback_layout.addLayout(button_layout)

        self.group_feedback.setLayout(feedback_layout)
        content_layout.addWidget(self.group_feedback)

        # Add content layout to main layout
        main_layout.addLayout(content_layout)

        self.load_feedback()

    def handle_back(self):
        """Xử lý khi người dùng nhấn nút Back"""
        # Nếu đang xử lý dữ liệu, hiển thị cảnh báo
        if self.is_processing():
            reply = QMessageBox.warning(
                self, 'Cảnh báo',
                'Đang có tiến trình xử lý dữ liệu đang chạy. Quay về màn hình chính sẽ dừng xử lý.\n\nBạn có chắc muốn quay lại không?',
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.No:
                return

            # Người dùng đã xác nhận, dừng xử lý
            self.cleanup()

        # Gọi callback để quay về màn hình chính
        if self.back_callback:
            self.back_callback()

    def goto_other_program(self, program_name: str):
        """Chuyển đến chương trình khác mà không cần hiển thị cảnh báo"""
        # Thông báo nếu có tiến trình đang chạy
        if self.is_processing():
            self.log("Tiến trình xử lý đang chạy trong nền và sẽ tiếp tục.")
            QMessageBox.information(
                self, 'Thông báo',
                'Tiến trình xử lý đang chạy trong nền và sẽ tiếp tục cho đến khi hoàn thành.'
            )

        # Lấy callback chuyển chương trình từ thuộc tính của lớp (trực tiếp gọi không qua goto_other_program)
        if self.goto_callback and callable(self.goto_callback):
            # Tạm lưu callback để tránh đệ quy
            callback = self.goto_callback
            callback(program_name)

    def show_database_manager(self):
        """Hiển thị dialog quản lý cơ sở dữ liệu"""
        dialog = DatabaseManagerDialog(self)
        dialog.exec()

    # Thêm phương thức xử lý sự kiện bàn phím
    def keyPressEvent(self, event):
        if event.key() == Qt.Key.Key_Backspace:
            self.handle_back()
        else:
            super().keyPressEvent(event)

    # Event filter để hiển thị menu khi di chuột đến nút
    def eventFilter(self, obj, event):
        if obj == self.context_button and event.type() == QEvent.Type.Enter:
            self.context_button.showMenu()
        return super().eventFilter(obj, event)

    def log(self, message: str):
        # Thêm timestamp cho thông báo log
        timestamp = datetime.now().strftime("%H:%M:%S")

        # Luôn ghi vào console cho debug
        print(f"[{timestamp}] {message}")

        # Đặc biệt xử lý thông báo định dạng mới với emoji 🏷️
        if "🏷️ Brand '" in message and "Phân loại: " in message:
            # Đây là thông báo tổng hợp đã được định dạng, hiển thị trực tiếp
            timestamped_message = f"[{timestamp}] {message}"
            self.log_text.append(timestamped_message)
            return

        # Danh sách các thông báo cụ thể hoặc mẫu cần bỏ qua hoàn toàn
        skip_messages = [
            "Đã xác định spreadsheet ID:",
            "Đã tạo mapping cho",
            "Chỉ số cột",
            "  - Đang phân loại sản phẩm:",
            "  - Kết quả phân loại:",
            "Đang xử lý brand",
            "Brand code '",
            "Đã truy cập worksheet thành công",
            "Đang đọc dữ liệu từ",
            "Đã đọc",
            "Xử lý đến dòng",
            "Sẽ xử lý",
            "Đang tạo mapping",
            "Đang kiểm tra tiến độ",
            "Worker hoạt động gần đây",
            "Đang xử lý '",
            "Đã xác định các chỉ số",
            "Thời điểm bắt đầu:",
            "Đã khởi tạo worker",
            "ProcessingRunnable đang bắt đầu",
            "Đã import thành công",
            "Đang truy cập worksheets",
            "Đã tải",
            "worksheet",
            "Đã xử lý",
            "Brand Sheet:",
            "Các cột Brand:",
            "Các cột Deal:",
            "Model sử dụng:",
            "Không tìm thấy sản phẩm",
            "Bỏ qua cập nhật",
            "Tổng số lần gọi API:",
            "Tổng token",
            "Bắt đầu quét",
            "ProcessingRunnable đã hoàn tất quá trình xử lý",
            "Xử lý hoàn tất",
            "Đã hoàn tất xử lý dữ liệu",
            "Cập nhật hoàn tất",
            "Xử lý dữ liệu hoàn tất"
        ]

        # Biến lưu trữ thông tin brand hiện tại để gộp log
        # Định dạng: {brand_code: {"categories": str, "product_count": int, "last_timestamp": str}}
        if not hasattr(self, '_brand_logs'):
            self._brand_logs = {}

        # Kiểm tra thông báo hoàn tất đặc biệt để thay thế bằng thông báo duy nhất
        if any(completion_text in message for completion_text in [
            "ProcessingRunnable đã hoàn tất quá trình xử lý",
            "Xử lý hoàn tất",
            "Đã hoàn tất xử lý dữ liệu",
            "Cập nhật hoàn tất",
            "Xử lý dữ liệu hoàn tất"
        ]):
            # Nếu thông báo chứa "Đã cập nhật X dòng", hiển thị thông báo này thay vì thông báo hoàn tất chung
            if "Đã cập nhật " in message and " dòng" in message:
                timestamped_message = f"[{timestamp}] ✅ {message}"
                self.log_text.append(timestamped_message)
                return

            # Bỏ qua các thông báo hoàn tất khác, thông báo tổng hợp sẽ được hiển thị ở nơi khác
            return

        # Bắt thông báo "Brand 'X' có Y sản phẩm cần phân loại"
        brand_info_pattern = r"Brand ['\"]([^'\"]+)['\"] có (\d+) sản phẩm cần phân loại"
        brand_info_match = re.search(brand_info_pattern, message)
        if brand_info_match:
            brand_code = brand_info_match.group(1)
            product_count = int(brand_info_match.group(2))

            # Lưu thông tin để kết hợp sau này
            if brand_code not in self._brand_logs:
                self._brand_logs[brand_code] = {}
            self._brand_logs[brand_code]["product_count_to_process"] = product_count
            self._brand_logs[brand_code]["last_timestamp"] = timestamp

            # Hiển thị log này để người dùng biết
            timestamped_message = f"[{timestamp}] 🔍 {message}"
            self.log_text.append(timestamped_message)
            return

        # Xử lý các thông báo liên quan đến phân loại brand
        brand_code_pattern = r"Brand code ['\"]([^'\"]+)['\"] -> (.+)"
        brand_completion_pattern = r"Hoàn tất phân loại brand ['\"]([^'\"]+)['\"] với (\d+) sản phẩm"
        brand_completion2_pattern = r"Hoàn tất phân loại brand ['\"]([^'\"]+)['\"]: (.+)"

        # Kiểm tra xem có phải thông báo phân loại brand không
        brand_match = re.search(brand_code_pattern, message)
        completion_match = re.search(brand_completion_pattern, message)
        completion2_match = re.search(brand_completion2_pattern, message)

        if brand_match:
            # Trích xuất thông tin
            brand_code = brand_match.group(1)
            categories = brand_match.group(2).strip()

            # Lưu thông tin cho brand này để kết hợp với thông báo số lượng sản phẩm
            if brand_code not in self._brand_logs:
                self._brand_logs[brand_code] = {}
            self._brand_logs[brand_code]["categories"] = categories
            self._brand_logs[brand_code]["last_timestamp"] = timestamp

            # Kiểm tra nếu đã có product_count, hiển thị ngay
            if "product_count" in self._brand_logs[brand_code]:
                product_count = self._brand_logs[brand_code]["product_count"]
                combined_message = f"[{timestamp}] 🏷️ Brand '{brand_code}': Đã xử lý {product_count} sản phẩm | Phân loại: {categories}"
                self.log_text.append(combined_message)
                del self._brand_logs[brand_code]
            elif "product_count_to_process" in self._brand_logs[brand_code]:
                # Nếu biết số lượng cần xử lý, hiển thị tạm thời
                product_count = self._brand_logs[brand_code]["product_count_to_process"]
                combined_message = f"[{timestamp}] 🏷️ Brand '{brand_code}': Đã xử lý {product_count} sản phẩm | Phân loại: {categories}"
                self.log_text.append(combined_message)
                del self._brand_logs[brand_code]
            # Nếu không, chờ thông tin hoàn tất
            return

        elif completion_match:
            # Trích xuất thông tin
            brand_code = completion_match.group(1)
            product_count = completion_match.group(2)

            # Thêm thông tin số lượng sản phẩm
            if brand_code not in self._brand_logs:
                self._brand_logs[brand_code] = {"categories": "", "last_timestamp": timestamp}
            self._brand_logs[brand_code]["product_count"] = int(product_count)

            # Hiển thị thông báo gộp
            if "categories" in self._brand_logs[brand_code]:
                categories = self._brand_logs[brand_code]["categories"]
                product_count = self._brand_logs[brand_code].get("product_count", 0)
                show_timestamp = self._brand_logs[brand_code].get("last_timestamp", timestamp)

                # Hiển thị thông báo kết hợp với biểu tượng và định dạng rõ ràng hơn
                combined_message = f"[{show_timestamp}] 🏷️ Brand '{brand_code}': Đã xử lý {product_count} sản phẩm | Phân loại: {categories}"
                self.log_text.append(combined_message)

                # Xóa thông tin đã hiển thị
                del self._brand_logs[brand_code]
            else:
                # Không có thông tin phân loại, hiển thị thông báo "đang chờ"
                # Làm rỗng, sẽ hiển thị khi có thông tin đầy đủ
                pass
            return

        elif completion2_match:
            # Đây là thông báo có định dạng cũ, nhưng vẫn xử lý để hiển thị theo định dạng mới
            brand_code = completion2_match.group(1)
            categories = completion2_match.group(2).strip()

            # Tìm số lượng sản phẩm từ thông tin đã lưu hoặc giả định là 0
            product_count = 0
            if brand_code in self._brand_logs and "product_count" in self._brand_logs[brand_code]:
                product_count = self._brand_logs[brand_code]["product_count"]
            elif brand_code in self._brand_logs and "product_count_to_process" in self._brand_logs[brand_code]:
                product_count = self._brand_logs[brand_code]["product_count_to_process"]

            # Hiển thị theo định dạng mới
            combined_message = f"[{timestamp}] 🏷️ Brand '{brand_code}': Đã xử lý {product_count} sản phẩm | Phân loại: {categories}"
            self.log_text.append(combined_message)

            # Xóa thông tin đã lưu nếu có
            if brand_code in self._brand_logs:
                del self._brand_logs[brand_code]
            return

        # Kiểm tra xem có nên bỏ qua thông báo này không
        for skip_text in skip_messages:
            if skip_text in message:
                return  # Không hiển thị lên UI

        # Hiển thị các thông báo quan trọng với biểu tượng phù hợp
        if "Bắt đầu xử lý dữ liệu" in message:
            timestamped_message = f"[{timestamp}] ▶️ {message}"
        elif "hoàn tất" in message.lower() and "xử lý" in message.lower():
            timestamped_message = f"[{timestamp}] ✅ {message}"
        elif "đã cập nhật" in message.lower() and "brand" in message.lower():
            timestamped_message = f"[{timestamp}] ✅ {message}"
        elif "lỗi" in message.lower() or "error" in message.lower():
            timestamped_message = f"[{timestamp}] ❌ {message}"
        else:
            # Nếu đến đây, đây là thông báo quan trọng khác không bị lọc
            timestamped_message = f"[{timestamp}] {message}"

        # Hiển thị thông báo đã lọc lên giao diện
        self.log_text.append(timestamped_message)


    # Thêm phương thức cleanup_logs để dọn dẹp log định kỳ
    def cleanup_logs(self):
        """Dọn dẹp các _brand_logs đã quá hạn để tránh rò rỉ bộ nhớ"""
        if not hasattr(self, '_brand_logs'):
            return

        current_time = datetime.now()
        # Dọn dẹp các brand đã lưu quá 5 phút
        timeout = 300  # 5 phút tính bằng giây

        # Lưu trữ tạm thời các brand cần xóa
        brands_to_remove = []

        for brand_code, data in self._brand_logs.items():
            if "last_timestamp" in data:
                # Lấy thời gian cuối từ timestamp
                try:
                    last_time = datetime.strptime(data["last_timestamp"], "%H:%M:%S")
                    last_time = last_time.replace(year=current_time.year, month=current_time.month, day=current_time.day)

                    # Nếu thời gian hiện tại < last_time, tức là đã qua ngày mới
                    if current_time < last_time:
                        last_time = last_time.replace(day=current_time.day-1)

                    # Tính thời gian đã trôi qua
                    delta = (current_time - last_time).total_seconds()

                    # Nếu quá timeout, đánh dấu xóa
                    if delta > timeout:
                        brands_to_remove.append(brand_code)

                        # Hiển thị thông báo về brand đã bị timeout
                        if "categories" in data:
                            categories = data["categories"]
                            product_count = data.get("product_count", data.get("product_count_to_process", 0))

                            # Hiển thị thông báo cảnh báo
                            warning_message = f"⚠️ Timeout: Brand '{brand_code}' có {product_count} sản phẩm | Phân loại: {categories}"
                            self.log(warning_message)
                except Exception as e:
                    # Nếu xảy ra lỗi khi xử lý thời gian, cũng xóa
                    brands_to_remove.append(brand_code)
            else:
                # Nếu không có thông tin thời gian, cũng xóa
                brands_to_remove.append(brand_code)

        # Xóa các brand đã đánh dấu
        for brand_code in brands_to_remove:
            del self._brand_logs[brand_code]

    def load_feedback(self):
        """Load feedback data from SQLite database"""
        try:
            conn = sqlite3.connect(str(DB_PATH))
            cursor = conn.cursor()
            cursor.execute("SELECT brand_code, classification, product_names FROM feedback")
            rows = cursor.fetchall()

            self.feedback_data = {row[0]: row[1] for row in rows}
            self.product_data = {row[0]: row[2] or "" for row in rows}

            # Update table with loaded data
            self.feedback_table.setRowCount(len(rows))
            for i, (brand_code, classification, product_names) in enumerate(rows):
                self.feedback_table.setItem(i, 0, QTableWidgetItem(brand_code))
                self.feedback_table.setItem(i, 1, QTableWidgetItem(classification))
                self.feedback_table.setItem(i, 2, QTableWidgetItem(product_names or ""))

            conn.close()
        except Exception as e:
            self.log(f"Error loading feedback: {e}")
            self.feedback_data = {}
            self.product_data = {}

    def update_feedback_table(self, result_data):
        """Update feedback table with brand_feedback and product buttons"""
        # Save the full details
        brand_feedback = result_data["feedback"]
        brand_products = result_data["products"]  # For backward compatibility
        self.product_details = result_data.get("product_details", {})  # Store for detail dialog

        # Clear existing data
        self.feedback_table.setRowCount(0)

        # Add each row
        for brand_code, classification in brand_feedback.items():
            self.add_feedback_row(brand_code, classification, self.product_details.get(brand_code, []))

    def add_feedback_row(self, brand_code, classification, product_details):
        """Add a single row to the feedback table"""
        # Get current row count
        current_row = self.feedback_table.rowCount()
        self.feedback_table.insertRow(current_row)

        # Brand code column
        item_code = QTableWidgetItem(brand_code)
        self.feedback_table.setItem(current_row, 0, item_code)

        # Classification column (editable)
        item_class = QTableWidgetItem(classification)
        item_class.setFlags(item_class.flags() | Qt.ItemFlag.ItemIsEditable)
        self.feedback_table.setItem(current_row, 1, item_class)

        # Product count and view button column
        product_count = len(product_details)

        widget = QWidget()
        widget.setMaximumWidth(200)  # Giới hạn chiều rộng tối đa của widget
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(5, 2, 5, 2)
        layout.setSpacing(5)  # Giảm khoảng cách giữa các thành phần

        count_label = QLabel(f"{product_count} sản phẩm")
        count_label.setMaximumWidth(80)  # Giới hạn chiều rộng của label

        view_button = QPushButton("Xem chi tiết")
        view_button.setMaximumWidth(100)  # Giới hạn chiều rộng của nút
        view_button.setFixedHeight(25)  # Chiều cao cố định
        view_button.clicked.connect(lambda _, code=brand_code: self.show_product_details(code))

        layout.addWidget(count_label)
        layout.addWidget(view_button)

        self.feedback_table.setCellWidget(current_row, 2, widget)

        # Store the product details
        self.product_details[brand_code] = product_details

        # Scroll to the new row - disabled to let user control scrolling
        # self.feedback_table.scrollToItem(item_code)

    def handle_item_processed(self, brand_code, classification, product_details):
        """Handle real-time feedback when an item is processed"""
        # Add the row to the table
        self.add_feedback_row(brand_code, classification, product_details)

        # Loại bỏ log trùng lặp (đã được gửi từ ProcessingWorker)
        # self.log(f"🏷️ Brand '{brand_code}': Đã xử lý {len(product_details)} sản phẩm | Phân loại: {classification}")

        # Process any pending events to update the UI - disabled to prevent auto scrolling
        # QApplication.processEvents()

    def show_product_details(self, brand_code):
        """Show dialog with detailed product information for editing"""
        if brand_code not in self.product_details:
            # Try to load from database
            products = get_product_classifications(brand_code)
            if not products:
                self.log(f"Không có dữ liệu sản phẩm cho {brand_code}")
                return
        else:
            products = self.product_details[brand_code]

        dialog = ProductDetailDialog(brand_code, products, self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # Update our stored data with any changes
            updated_data = dialog.get_updated_data()
            self.product_details[brand_code] = updated_data

            # Update the main classification based on the updated products
            unique_classifications = set()
            for _, classification in updated_data:
                unique_classifications.add(classification)

            new_classification = ", ".join(unique_classifications)

            # Find and update the row in the table
            for row in range(self.feedback_table.rowCount()):
                code_item = self.feedback_table.item(row, 0)
                if code_item and code_item.text() == brand_code:
                    self.feedback_table.item(row, 1).setText(new_classification)
                    break

            self.log(f"Đã cập nhật phân loại sản phẩm cho {brand_code}")

    def save_feedback(self):
        """Đồng bộ dữ liệu vào database và cập nhật Brand list nếu được chọn"""
        try:
            # Hiển thị dialog xác nhận
            confirm_dialog = QDialog(self)
            confirm_dialog.setWindowTitle("Xác nhận đồng bộ")
            confirm_dialog.setFixedWidth(300)

            layout = QVBoxLayout(confirm_dialog)

            # Thêm label thông báo
            message_label = QLabel("Chọn các tùy chọn đồng bộ:")
            layout.addWidget(message_label)

            # Thêm checkbox cho cập nhật database
            update_db_checkbox = QCheckBox("Cập nhật Database")
            update_db_checkbox.setChecked(True)  # Mặc định được chọn
            layout.addWidget(update_db_checkbox)

            # Thêm checkbox cho cập nhật Brand list
            update_brand_checkbox = QCheckBox("Cập nhật Brand list")
            update_brand_checkbox.setChecked(True)  # Mặc định được chọn
            layout.addWidget(update_brand_checkbox)

            # Thêm checkbox để chỉ cập nhật thay đổi
            update_changes_only_checkbox = QCheckBox("Chỉ cập nhật các Brand đã thay đổi")
            update_changes_only_checkbox.setChecked(True)  # Mặc định được chọn
            layout.addWidget(update_changes_only_checkbox)

            # Thêm nút bấm
            button_layout = QHBoxLayout()
            ok_button = QPushButton("Đồng ý")
            cancel_button = QPushButton("Hủy")

            ok_button.clicked.connect(confirm_dialog.accept)
            cancel_button.clicked.connect(confirm_dialog.reject)

            button_layout.addWidget(ok_button)
            button_layout.addWidget(cancel_button)
            layout.addLayout(button_layout)

            # Hiển thị dialog và kiểm tra kết quả
            if confirm_dialog.exec() != QDialog.DialogCode.Accepted:
                self.log("Đã hủy đồng bộ dữ liệu")
                return

            # Chuẩn bị dữ liệu trước
            row_count = self.feedback_table.rowCount()
            updated_brands = []
            updated_terms = 0
            feedback_data = []
            unsync_brands = []

            # Hiển thị LoadingDialog cho quá trình chuẩn bị dữ liệu
            loading_dialog = LoadingDialog(self, "Đang chuẩn bị dữ liệu đồng bộ...")
            loading_dialog.show()
            QApplication.processEvents()

            # Lấy danh sách các brand chưa đồng bộ nếu cần
            if update_changes_only_checkbox.isChecked():
                try:
                    conn = sqlite3.connect(str(DB_PATH))
                    cursor = conn.cursor()
                    cursor.execute("SELECT brand_code FROM sync_status WHERE sync_status = 0")
                    unsync_brands = [row[0] for row in cursor.fetchall()]
                    conn.close()

                    if unsync_brands:
                        self.log(f"Tìm thấy {len(unsync_brands)} brand cần được đồng bộ")
                    else:
                        self.log("Không có brand nào cần đồng bộ")
                except Exception as e:
                    self.log(f"Lỗi khi truy vấn trạng thái đồng bộ: {e}")
                    unsync_brands = []  # Reset trong trường hợp lỗi

            for row in range(row_count):
                brand_item = self.feedback_table.item(row, 0)
                class_item = self.feedback_table.item(row, 1)

                if brand_item and class_item:
                    brand_code = brand_item.text().strip()
                    user_classification = class_item.text().strip()

                    if brand_code:
                        # Lấy chi tiết sản phẩm
                        product_details = self.product_details.get(brand_code, [])
                        product_names_str = " | ".join([name for name, _ in product_details[:3]])

                        # Thêm vào danh sách batch cập nhật database
                        feedback_data.append((brand_code, user_classification, product_names_str))

                        # Kiểm tra xem brand có nằm trong danh sách cần đồng bộ không
                        should_update = True

                        # Nếu chỉ cập nhật thay đổi và có danh sách brand chưa đồng bộ
                        if update_changes_only_checkbox.isChecked() and unsync_brands:
                            if brand_code not in unsync_brands:
                                should_update = False

                        # Kiểm tra thay đổi so với dữ liệu hiện tại
                        original_value = self.feedback_data.get(brand_code, "")
                        if original_value != user_classification:
                            if should_update:
                                updated_brands.append((brand_code, user_classification))

                            # Cập nhật custom mapping nếu có thay đổi
                            if original_value:
                                orig_tokens = [t.strip().lower() for t in original_value.split(",") if t.strip()]
                                user_tokens = [t.strip().lower() for t in user_classification.split(",") if t.strip()]

                                context = product_names_str

                                for orig, user_tok in zip(orig_tokens, user_tokens):
                                    if orig != user_tok:
                                        # Ghi log thay đổi phân loại
                                        self.log(f"Thay đổi phân loại: '{orig}' -> '{user_tok.capitalize()}'")
                                        updated_terms += 1

            # Đóng loading_dialog cho phần chuẩn bị
            loading_dialog.accept()

            # 1. Lưu vào database nếu được chọn
            if update_db_checkbox.isChecked():
                # Hiển thị loading dialog cho việc lưu database
                db_loading_dialog = LoadingDialog(self, "Đang cập nhật cơ sở dữ liệu...")
                db_loading_dialog.show()
                QApplication.processEvents()

                conn = sqlite3.connect(str(DB_PATH))
                cursor = conn.cursor()

                # Bắt đầu transaction
                cursor.execute("BEGIN TRANSACTION")

                # Thực hiện batch insert
                if feedback_data:
                    cursor.executemany(
                        "INSERT OR REPLACE INTO feedback (brand_code, classification, product_names) VALUES (?, ?, ?)",
                        feedback_data
                    )

                # Commit transaction
                conn.commit()
                conn.close()

                # Đóng loading_dialog cho db
                db_loading_dialog.accept()

                self.log(f"Đã cập nhật database: {len(updated_brands)} brand được cập nhật, {updated_terms} thay đổi phân loại")

            # 2. Cập nhật Brand list nếu được chọn và có thay đổi
            if update_brand_checkbox.isChecked() and updated_brands:
                # Hiển thị dialog chi tiết với thông tin progress
                progress_dialog = QDialog(self)
                progress_dialog.setWindowTitle("Cập nhật Google Sheet")
                progress_dialog.setFixedSize(450, 150)
                progress_dialog.setWindowFlags(progress_dialog.windowFlags() & ~Qt.WindowType.WindowContextHelpButtonHint)

                progress_layout = QVBoxLayout(progress_dialog)

                status_label = QLabel("Đang chuẩn bị cập nhật...")
                status_label.setStyleSheet("font-weight: bold; font-size: 13px;")
                progress_layout.addWidget(status_label)

                # Thêm icon loading
                info_label = QLabel("Đang kết nối đến Google Sheet. Quá trình này có thể mất vài phút...")
                info_label.setStyleSheet("color: #666;")
                progress_layout.addWidget(info_label)

                progress_bar = QProgressBar()
                progress_bar.setRange(0, len(updated_brands))
                progress_bar.setValue(0)
                progress_bar.setStyleSheet("""
                    QProgressBar {
                        border: none;
                        background-color: #E0E0E0;
                        height: 4px;
                        text-align: center;
                    }
                    QProgressBar::chunk {
                        background-color: #2196F3;
                    }
                """)
                progress_layout.addWidget(progress_bar)

                detail_label = QLabel("Đang xử lý...")
                detail_label.setWordWrap(True)
                progress_layout.addWidget(detail_label)

                note_label = QLabel("Lưu ý: Google có giới hạn API nên quá trình này sẽ tạm dừng giữa các đợt cập nhật. Vui lòng không đóng cửa sổ này.")
                note_label.setStyleSheet("color: #FF5722; font-style: italic;")
                note_label.setWordWrap(True)
                progress_layout.addWidget(note_label)

                # Hiển thị dialog
                progress_dialog.show()
                QApplication.processEvents()

                class UpdateSheetWorker(QObject):
                    finished = pyqtSignal(int, str)
                    progress = pyqtSignal(int, int)

                    def __init__(self, spreadsheet, sheet_name, brand_code_col, brand_type_col, updated_brands, spreadsheet_id):
                        super().__init__()
                        self.spreadsheet = spreadsheet
                        self.sheet_name = sheet_name
                        self.brand_code_col = brand_code_col
                        self.brand_type_col = brand_type_col
                        self.updated_brands = updated_brands
                        self.spreadsheet_id = spreadsheet_id

                    def run(self):
                        try:
                            # Tìm worksheet
                            try:
                                brand_sheet = self.spreadsheet.worksheet(self.sheet_name)
                            except Exception as e:
                                self.finished.emit(0, f"Không tìm thấy sheet '{self.sheet_name}': {str(e)}")
                                return

                            # Lấy tất cả dữ liệu từ Brand list
                            try:
                                brand_data = brand_sheet.get_all_values()
                                if len(brand_data) < 2:
                                    self.finished.emit(0, "Không có dữ liệu trong Brand list")
                                    return
                            except Exception as e:
                                self.finished.emit(0, f"Lỗi khi đọc dữ liệu từ sheet: {str(e)}")
                                return

                            # Tạo mapping brand_code -> row_index
                            try:
                                brand_code_idx = ord(self.brand_code_col) - ord('A')
                                brand_type_idx = ord(self.brand_type_col) - ord('A')
                                brand_mapping = {row[brand_code_idx]: i for i, row in enumerate(brand_data[1:], start=2)}
                            except Exception as e:
                                self.finished.emit(0, f"Lỗi khi phân tích cột dữ liệu: {str(e)}")
                                return

                            # Chia các cập nhật thành các nhóm để giảm số lượng API calls
                            batch_size = 5  # Giảm còn 5 dòng một lần
                            update_count = 0

                            # Lọc ra các brand có trong sheet trước khi chia batch
                            brands_to_update = []
                            for brand_code, new_classification in self.updated_brands:
                                if brand_code in brand_mapping:
                                    row_idx = brand_mapping[brand_code]
                                    brands_to_update.append((brand_code, new_classification, row_idx))

                            # Chia thành các batch nhỏ hơn
                            batches = [brands_to_update[i:i+batch_size] for i in range(0, len(brands_to_update), batch_size)]

                            # Chuẩn bị kết nối database để cập nhật trạng thái đồng bộ
                            conn = sqlite3.connect(str(DB_PATH))
                            cursor = conn.cursor()

                            # Xử lý từng batch
                            for batch_idx, batch in enumerate(batches):
                                batch_updates = []
                                current_batch_brands = []

                                for brand_code, new_classification, row_idx in batch:
                                    batch_updates.append({
                                        'range': f'{self.brand_type_col}{row_idx}',
                                        'values': [[new_classification]]
                                    })
                                    current_batch_brands.append((brand_code, new_classification))

                                # Cập nhật progress trước khi bắt đầu xử lý batch
                                self.progress.emit(update_count, len(batch))

                                # Sử dụng batch_update để cập nhật nhiều ô một lúc
                                try:
                                    brand_sheet.batch_update(batch_updates)
                                    update_count += len(batch)

                                    # Cập nhật trạng thái đồng bộ trong database
                                    try:
                                        for brand_code, classification in current_batch_brands:
                                            # Thực hiện cập nhật trạng thái đồng bộ (1 = đã đồng bộ)
                                            cursor.execute(
                                                """UPDATE sync_status SET
                                                   sync_status = 1,
                                                   last_sync_time = datetime('now'),
                                                   last_classification = ?,
                                                   spreadsheet_id = ?
                                                   WHERE brand_code = ?""",
                                                (classification, self.spreadsheet_id, brand_code)
                                            )
                                    except Exception as db_error:
                                        print(f"Lỗi cập nhật trạng thái đồng bộ: {db_error}")

                                    # Thêm độ trễ tỉ lệ với kích thước batch để tránh vượt quá API quota
                                    sleep_time = min(len(batch) * 0.3, 2)  # Tối đa 2 giây
                                    time.sleep(sleep_time)
                                except Exception as e:
                                    # Đóng kết nối database trước khi trả về lỗi
                                    conn.close()
                                    self.finished.emit(update_count, f"Lỗi cập nhật batch {batch_idx+1}/{len(batches)}: {e}")
                                    return

                            # Commit các thay đổi trạng thái đồng bộ
                            conn.commit()
                            conn.close()

                            self.finished.emit(update_count, "")

                        except Exception as e:
                            self.finished.emit(0, str(e))

                # Lấy spreadsheet ID
                spreadsheet_id = ""
                if hasattr(self.spreadsheet, 'id'):
                    spreadsheet_id = self.spreadsheet.id

                # Tạo worker và thread
                self.update_thread = QThread()
                self.update_worker = UpdateSheetWorker(
                    self.spreadsheet,
                    self.brand_sheet_combo.currentText(),
                    self.brand_code_input.text().strip().upper(),
                    self.brand_type_input.text().strip().upper(),
                    updated_brands,
                    spreadsheet_id
                )
                self.update_worker.moveToThread(self.update_thread)

                # Kết nối signals
                self.update_thread.started.connect(self.update_worker.run)
                self.update_worker.finished.connect(self.update_thread.quit)
                self.update_worker.finished.connect(lambda count, error: self.handle_sheet_update_finished(count, error, progress_dialog))
                self.update_worker.progress.connect(lambda current, batch: self.update_progress_dialog(progress_bar, status_label, detail_label, current, batch))

                # Cleanup
                self.update_thread.finished.connect(self.update_worker.deleteLater)
                self.update_thread.finished.connect(self.update_thread.deleteLater)

                # Bắt đầu thread và hiển thị dialog
                self.update_thread.start()
            else:
                # Reload feedback để refresh dữ liệu
                self.load_feedback()

        except Exception as e:
            self.log(f"Lỗi đồng bộ dữ liệu: {e}")
            QMessageBox.critical(self, "Lỗi", f"Không thể đồng bộ dữ liệu: {e}")

    def handle_sheet_update_finished(self, count, error, dialog):
        """Xử lý khi cập nhật sheet hoàn tất"""
        dialog.accept()  # Đóng dialog tiến trình

        if error:
            self.log(f"Lỗi cập nhật Brand list: {error}")
            QMessageBox.warning(self, "Cảnh báo", f"Không thể cập nhật Brand list: {error}")
        else:
            self.log(f"Đã cập nhật {count} brand trong Brand list")

            # Hiển thị thông báo hoàn thành đẹp mắt
            success_dialog = QDialog(self)
            success_dialog.setWindowTitle("Cập nhật thành công")
            success_dialog.setFixedSize(350, 150)
            success_dialog.setWindowFlags(success_dialog.windowFlags() & ~Qt.WindowType.WindowContextHelpButtonHint)

            success_layout = QVBoxLayout(success_dialog)

            # Tiêu đề
            title_label = QLabel("Đồng bộ thành công! ✅")
            title_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #4CAF50;")
            success_layout.addWidget(title_label)

            # Thông tin chi tiết
            detail_label = QLabel(f"Đã cập nhật {count} brand trên Google Sheet.\nDữ liệu đã được đồng bộ hóa thành công.")
            detail_label.setWordWrap(True)
            success_layout.addWidget(detail_label)

            # Thêm khoảng trống
            success_layout.addSpacing(10)

            # Nút đóng
            button_layout = QHBoxLayout()
            ok_button = QPushButton("OK")
            ok_button.setStyleSheet("""
                QPushButton {
                    background-color: #4CAF50;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 5px 20px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #45a049;
                }
            """)
            ok_button.clicked.connect(success_dialog.accept)
            button_layout.addStretch()
            button_layout.addWidget(ok_button)
            button_layout.addStretch()

            success_layout.addLayout(button_layout)

            # Hiển thị dialog
            success_dialog.exec()

        # Reload feedback để refresh dữ liệu
        self.load_feedback()

    def update_progress_dialog(self, progress_bar, status_label, detail_label, current, batch):
        """Cập nhật thông tin trên dialog tiến trình"""
        total = progress_bar.maximum()
        progress_bar.setValue(current + batch)

        # Tính phần trăm hoàn thành
        percent = int((current + batch) / total * 100) if total > 0 else 0

        # Hiển thị thông tin chi tiết và cập nhật status label
        status_label.setText(f"Đang cập nhật Google Sheet ({current + batch}/{total}) - {percent}% hoàn thành")

        # Hiển thị thông tin batch và thời gian ước tính
        batch_info = f"Đang đồng bộ {batch} brand..."

        # Tính thời gian còn lại ước tính (giả định mỗi batch mất khoảng 1-2 giây)
        remaining_items = total - (current + batch)
        remaining_batches = remaining_items / batch if batch > 0 else 0
        est_seconds = remaining_batches * 2

        time_info = ""
        if est_seconds > 60:
            minutes = int(est_seconds // 60)
            seconds = int(est_seconds % 60)
            time_info = f"\nƯớc tính còn khoảng {minutes} phút {seconds} giây"
        elif est_seconds > 0:
            time_info = f"\nƯớc tính còn khoảng {int(est_seconds)} giây"

        detail_label.setText(f"{batch_info}{time_info}")

        # Cập nhật UI ngay lập tức
        QApplication.processEvents()

    def on_sheet_url_changed(self):
        """Auto-parse spreadsheet ID when URL changes"""
        url = self.sheet_link_input.text().strip()
        if url:
            spreadsheet_id = parse_spreadsheet_id(url)
            if spreadsheet_id and url != spreadsheet_id:
                # Replace the URL with just the spreadsheet ID in the input field
                self.sheet_link_input.blockSignals(True)  # Prevent recursive signal triggering
                self.sheet_link_input.setText(spreadsheet_id)
                self.sheet_link_input.blockSignals(False)

    def load_sheet(self):
        """Load Google Sheet sử dụng QThreadPool"""
        from thread_pool_fix import SheetLoaderRunnable

        sheet_url = self.sheet_link_input.text().strip()
        if not sheet_url:
            QMessageBox.warning(self, "Warning", "Nhập link Google Sheet!")
            return

        try:
            # Vô hiệu hóa nút Load Sheet và thay đổi text
            self.load_sheet_btn.setEnabled(False)
            self.load_sheet_btn.setText("Đang tải...")
            self.sheet_link_input.setEnabled(False)  # Vô hiệu hóa cả text input

            # Try to parse spreadsheet ID, but also accept direct ID input
            spreadsheet_id = sheet_url
            if '/' in sheet_url:
                parsed_id = parse_spreadsheet_id(sheet_url)
                if parsed_id:
                    spreadsheet_id = parsed_id
                    self.log(f"Parsed spreadsheet ID: {spreadsheet_id}")

            # Create worker for loading sheet
            self.sheet_worker = SheetLoaderRunnable(spreadsheet_id, OAUTH2_BASE64)

            # Show loading dialog
            self.loading_dialog = LoadingDialog(self, f"Đang tải Google Sheet...")
            self.loading_dialog.show()  # Hiển thị dialog ngay lập tức

            # Start the runnable in threadpool
            QThreadPool.globalInstance().start(self.sheet_worker)

            # Timer to check for completion
            self.sheet_timer = QTimer()
            self.sheet_timer.timeout.connect(self.check_sheet_result)
            self.sheet_timer.start(500)  # Check every 500ms
        except Exception as e:
            # Khôi phục trạng thái nút và input field
            self.load_sheet_btn.setEnabled(True)
            self.load_sheet_btn.setText("Load Sheet")
            self.sheet_link_input.setEnabled(True)

            # Sử dụng thông báo lỗi thân thiện thay vì hiển thị lỗi kỹ thuật
            show_user_friendly_error(
                self,
                e,
                "Không thể tải Google Sheet. Vui lòng kiểm tra lại đường dẫn và quyền truy cập."
            )
            self.log(f"Error loading sheet: {e}")

    def check_sheet_result(self):
        """Kiểm tra kết quả từ sheet worker"""
        if not hasattr(self, 'sheet_worker') or not self.sheet_worker:
            self.sheet_timer.stop()
            # Khôi phục trạng thái nút và input field trong trường hợp lỗi
            self.load_sheet_btn.setEnabled(True)
            self.load_sheet_btn.setText("Load Sheet")
            self.sheet_link_input.setEnabled(True)
            return

        result = self.sheet_worker.result

        # Process any logs
        if hasattr(result, 'logs') and result.logs:
            for log in result.logs:
                self.log(log)
            result.logs = []  # Clear processed logs

        # Check if completed
        if result.is_completed:
            self.sheet_timer.stop()

            # Khôi phục trạng thái nút và input field
            self.load_sheet_btn.setEnabled(True)
            self.load_sheet_btn.setText("Load Sheet")
            self.sheet_link_input.setEnabled(True)

            if result.error:
                self.log(f"Error loading sheet: {result.error}")
                self.handle_sheet_error(result.error)
            else:
                self.handle_sheet_loaded(result.result)

    def handle_sheet_loaded(self, result):
        """Handle when sheet is loaded successfully"""
        gs_manager, spreadsheet = result

        # Đóng loading dialog nếu còn hiển thị
        if hasattr(self, 'loading_dialog') and self.loading_dialog.isVisible():
            self.loading_dialog.accept()

        if not spreadsheet:
            return  # Error was already shown

        self.gs_manager = gs_manager
        self.spreadsheet = spreadsheet
        self.log(f"Load Google Sheet thành công: {spreadsheet.id}")

        # Update sheet dropdowns with ALL worksheets
        sheet_titles = [ws.title for ws in self.spreadsheet.worksheets()]
        self.brand_sheet_combo.clear()
        self.deal_sheet_combo.clear()

        # Thêm tất cả các worksheet vào cả hai combo box
        self.brand_sheet_combo.addItems(sheet_titles)
        self.deal_sheet_combo.addItems(sheet_titles)

        # Tìm và đặt default selection nếu có sheet phù hợp
        brand_index = self.brand_sheet_combo.findText("Brand list")
        if brand_index >= 0:
            self.brand_sheet_combo.setCurrentIndex(brand_index)

        deal_index = self.deal_sheet_combo.findText("Deal list")
        if deal_index >= 0:
            self.deal_sheet_combo.setCurrentIndex(deal_index)

        self.log(f"Đã tải {len(sheet_titles)} worksheets từ Google Sheet")

    def handle_sheet_error(self, error_message):
        """Handle error while loading sheet"""
        self.log(f"Error detail: {error_message}")

        # Make sure dialog is closed
        if hasattr(self, 'loading_dialog') and self.loading_dialog.isVisible():
            self.loading_dialog.accept()

        # Sử dụng thông báo lỗi thân thiện thay vì hiển thị lỗi kỹ thuật trực tiếp
        show_user_friendly_error(
            self,
            error_message,
            "Có lỗi xảy ra khi tải dữ liệu từ Google Sheet. Vui lòng kiểm tra lại đường dẫn và quyền truy cập."
        )

    def start_processing(self):
        """Bắt đầu xử lý dữ liệu từ Google Sheets"""
        try:
            # Lấy thông tin từ giao diện người dùng
            brand_sheet_name = self.brand_sheet_combo.currentText()
            deal_sheet_name = self.deal_sheet_combo.currentText()

            # Nếu người dùng để trống, hiển thị thông báo lỗi
            if not brand_sheet_name or not deal_sheet_name:
                QMessageBox.warning(self, "Lỗi", "Vui lòng chọn Brand sheet và Deal sheet trước khi xử lý")
                return

            brand_code_col = self.brand_code_input.text().strip().upper()
            brand_type_col = self.brand_type_input.text().strip().upper()
            brand_name_col = self.brand_name_input.text().strip().upper()

            deal_brand_code_col = self.deal_brand_code_input.text().strip().upper()
            deal_product_col = self.deal_product_input.text().strip().upper()
            deal_price_col = self.deal_price_input.text().strip().upper()
            deal_pick_col = self.deal_pick_input.text().strip().upper()

            last_row = self.last_row_input.text().strip()
            model = self.model_combo.currentText()

            # Lấy ngày cho phiên live từ QDateEdit
            live_session_date = self.live_session_date.date()
            live_session = f"{live_session_date.day()}.{live_session_date.month()}"

            # Lưu threshold nếu được cung cấp
            threshold_text = self.similarity_threshold.text().strip()
            if threshold_text:
                try:
                    threshold = float(threshold_text)
                    if 0 <= threshold <= 1:
                        save_similarity_threshold(threshold)
                    else:
                        self.log(f"Cảnh báo: Ngưỡng tương đồng {threshold} không hợp lệ. Giá trị phải từ 0-1.")
                except ValueError:
                    self.log(f"Cảnh báo: Không thể chuyển đổi '{threshold_text}' thành số thập phân.")

            # Hiển thị thông báo bắt đầu xử lý
            self.log(f"Bắt đầu xử lý dữ liệu với các thông số:")
            self.log(f"- Brand Sheet: {brand_sheet_name}")
            self.log(f"- Deal Sheet: {deal_sheet_name}")
            self.log(f"- Các cột Brand: Code={brand_code_col}, Type={brand_type_col}, Name={brand_name_col}")
            self.log(f"- Các cột Deal: Brand={deal_brand_code_col}, Product={deal_product_col}, Price={deal_price_col}, Pick={deal_pick_col}")
            self.log(f"- Model sử dụng: {model}")
            self.log(f"- Phiên Live: {live_session}")

            # Clear old feedback
            self.feedback_table.setRowCount(0)
            self.product_details = {}

            # Create a QThread
            self.processing_thread = QThread()
            self.processing_worker = ProcessingWorker(
                self.spreadsheet,
                brand_sheet_name,
                deal_sheet_name,
                brand_code_col,
                brand_type_col,
                deal_brand_code_col,
                deal_product_col,
                last_row,
                model,
                brand_name_col,
                deal_price_col,
                deal_pick_col
            )

            # Đặt thông tin phiên live cho worker
            self.processing_worker.live_session = live_session

            self.processing_worker.moveToThread(self.processing_thread)

            # Connect signals
            self.processing_thread.started.connect(self.processing_worker.run)
            self.processing_worker.finished_signal.connect(self.processing_finished)
            self.processing_worker.log_signal.connect(self.log)
            # Connect the new signal for real-time updates
            self.processing_worker.item_processed_signal.connect(self.handle_item_processed)
            self.processing_worker.finished_signal.connect(self.processing_thread.quit)

            # Cleanup
            self.processing_thread.finished.connect(self.processing_worker.deleteLater)
            self.processing_thread.finished.connect(self.processing_thread.deleteLater)
            self.processing_thread.finished.connect(lambda: self.process_btn.setEnabled(True))
            self.processing_thread.finished.connect(lambda: self.pause_resume_btn.setEnabled(False))
            self.processing_thread.finished.connect(lambda: self.pause_resume_btn.setText("Tạm dừng"))

            # Vô hiệu hóa nút xử lý và hiển thị nút tạm dừng
            self.process_btn.setEnabled(False)
            self.pause_resume_btn.setEnabled(True)

            # Bắt đầu xử lý
            self.processing_thread.start()

            # Reset biến theo dõi trạng thái
            self.is_paused = False

        except Exception as e:
            self.log(f"Lỗi khi bắt đầu xử lý: {str(e)}")
            show_user_friendly_error(
                self,
                str(e),
                "Không thể khởi tạo quá trình xử lý dữ liệu. Vui lòng kiểm tra lại các thông số đầu vào."
            )

    def check_processing_result(self):
        """Phương thức này không còn được sử dụng - giữ lại để tương thích với code cũ"""
        self.log("Phương thức check_processing_result đã được thay thế bằng processing_finished")
        pass

    def processing_finished(self, result):
        """Xử lý kết quả khi worker hoàn thành"""
        try:
            # Bật lại nút xử lý, tắt nút tạm dừng
            self.process_btn.setEnabled(True)
            self.pause_resume_btn.setEnabled(False)
            self.pause_resume_btn.setText("Tạm dừng")

            if result:
                # Nếu result có lỗi thì hiển thị
                if isinstance(result, dict) and "error" in result:
                    self.log(f"Lỗi trong quá trình xử lý: {result['error']}")
                    QMessageBox.warning(self, "Lỗi xử lý", f"Đã xảy ra lỗi trong quá trình xử lý dữ liệu: {result['error']}")
                    return

                # Nếu có danh sách brand đã phân loại
                if isinstance(result, dict) and "feedback" in result:
                    self.feedback_data = result["feedback"]

                    # Lưu chiều rộng hiện tại của cột "Sản phẩm" trước khi cập nhật
                    current_col_width = self.feedback_table.columnWidth(2)

                    # Cập nhật bảng feedback với dữ liệu mới
                    self.update_feedback_table(result)

                    # Đặt lại chiều rộng cột "Sản phẩm" sau khi cập nhật để tránh bị giãn
                    self.feedback_table.setColumnWidth(2, current_col_width)

                # Log kết quả hoàn thành
                self.log("Quá trình xử lý dữ liệu đã hoàn tất")
            else:
                self.log("Hoàn tất xử lý nhưng không nhận được kết quả")

            # Cleanup _brand_logs để tránh trường hợp log bị bỏ sót
            self.cleanup_logs()

        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            self.log(f"Lỗi khi xử lý kết quả hoàn thành: {str(e)}")
            self.log(f"Chi tiết lỗi: {error_traceback}")

            # Hiển thị thông báo lỗi nhưng KHÔNG đóng ứng dụng
            QMessageBox.critical(
                self,
                "Lỗi hệ thống",
                f"Đã xảy ra lỗi trong quá trình kiểm tra kết quả xử lý:\n{str(e)}\n\nỨng dụng vẫn hoạt động, nhưng quá trình xử lý có thể không hoàn tất."
            )

    def show_similarity_database(self):
        """Show dialog with similarity database information"""
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        cursor.execute("SELECT product_name, similar_to, classification, similarity_score FROM similar_products ORDER BY similarity_score DESC")
        similarities = cursor.fetchall()
        conn.close()

        if not similarities:
            self.log("Chưa có dữ liệu tương đồng trong hệ thống")
            return

        dialog = QDialog(self)
        dialog.setWindowTitle("Cơ sở dữ liệu tương đồng")
        dialog.setMinimumSize(800, 500)

        layout = QVBoxLayout()

        # Similarity table
        table = QTableWidget()
        table.setColumnCount(4)
        table.setHorizontalHeaderLabels(["Tên sản phẩm", "Tương đồng với", "Phân loại", "Độ tương đồng"])

        table.setRowCount(len(similarities))
        for i, (name, similar_to, classification, score) in enumerate(similarities):
            table.setItem(i, 0, QTableWidgetItem(name))
            table.setItem(i, 1, QTableWidgetItem(similar_to))
            table.setItem(i, 2, QTableWidgetItem(classification))
            table.setItem(i, 3, QTableWidgetItem(f"{score:.2f}"))

        table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)

        layout.addWidget(table)

        # Close button
        close_btn = QPushButton("Đóng")
        close_btn.clicked.connect(dialog.accept)
        layout.addWidget(close_btn)

        dialog.setLayout(layout)
        dialog.exec()

    def closeEvent(self, event):
        """Override closeEvent to ensure proper cleanup"""
        # No need to manually stop threads, QThreadPool handles it
        super().closeEvent(event)

    def cleanup(self):
        """Clean up resources before destroying widget"""
        try:
            # Ghi log để theo dõi
            self.log("Đang dọn dẹp tài nguyên...")

            # Dừng các timer đang chạy
            timers_to_stop = []
            if hasattr(self, 'processing_timer') and self.processing_timer and self.processing_timer.isActive():
                timers_to_stop.append("processing_timer")
                self.processing_timer.stop()

            if hasattr(self, 'sheet_timer') and self.sheet_timer and self.sheet_timer.isActive():
                timers_to_stop.append("sheet_timer")
                self.sheet_timer.stop()

            if timers_to_stop:
                self.log(f"Đã dừng các timer: {', '.join(timers_to_stop)}")

            # Hủy các worker đang chạy
            workers_to_cancel = []

            # Dừng processing_worker
            if hasattr(self, 'processing_worker') and self.processing_worker:
                if hasattr(self.processing_worker, 'cancel'):
                    workers_to_cancel.append("processing_worker")
                    self.processing_worker.cancel()
                    self.processing_worker = None

            # Dừng sheet_worker
            if hasattr(self, 'sheet_worker') and self.sheet_worker:
                if hasattr(self.sheet_worker, 'cancel'):
                    workers_to_cancel.append("sheet_worker")
                    self.sheet_worker.cancel()
                    self.sheet_worker = None

            # Dừng thread
            if hasattr(self, 'processing_thread') and self.processing_thread and self.processing_thread.isRunning():
                self.log("Đang dừng processing_thread...")
                # Gửi tín hiệu kết thúc
                self.processing_thread.quit()
                # Chờ thread kết thúc với timeout
                if self.processing_thread.wait(2000):  # Chờ 2 giây
                    self.log("Đã dừng processing_thread thành công")
                else:
                    self.log("Không thể dừng processing_thread, thử dùng terminate")
                    self.processing_thread.terminate()
                    if self.processing_thread.wait(1000):
                        self.log("Đã terminate processing_thread thành công")
                    else:
                        self.log("CẢNH BÁO: Không thể kết thúc processing_thread")

            if workers_to_cancel:
                self.log(f"Đã hủy các worker: {', '.join(workers_to_cancel)}")

            # Đảm bảo threadpool hoàn thành
            wait_time = 2000  # 2 giây
            self.log("Đang đợi ThreadPool hoàn thành tác vụ...")
            if not self.thread_pool.waitForDone(wait_time):
                self.log(f"ThreadPool chưa hoàn thành trong {wait_time/1000} giây. Cố gắng dừng tất cả thread đang chạy.")
                # Thực hiện các bước để dừng mạnh hơn
                if hasattr(self.thread_pool, 'clear'):
                    self.thread_pool.clear()
                    self.log("Đã xóa tất cả task trong ThreadPool")
            else:
                self.log("ThreadPool đã hoàn thành tất cả các tác vụ.")

            # Gọi từ thread_pool_fix.cleanup_signals để dừng mọi worker đang chạy
            try:
                self.log("Đang dọn dẹp tất cả worker đang chạy...")
                from thread_pool_fix import cleanup_signals
                cleanup_signals()
                self.log("Đã dọn dẹp tất cả tín hiệu từ Thread Pool.")
            except Exception as e:
                self.log(f"Lỗi khi dọn dẹp tín hiệu: {str(e)}")

        except Exception as e:
            print(f"Lỗi trong quá trình cleanup: {str(e)}")
            self.log(f"Lỗi trong quá trình cleanup: {str(e)}")

        # Ghi log kết thúc
        self.log("Đã hoàn tất dọn dẹp tài nguyên.")

    def is_processing(self):
        """Kiểm tra xem có đang xử lý dữ liệu không"""
        # Kiểm tra timer xử lý đang chạy
        if hasattr(self, 'processing_timer') and self.processing_timer and self.processing_timer.isActive():
            return True

        # Kiểm tra timer load sheet đang chạy
        if hasattr(self, 'sheet_timer') and self.sheet_timer and self.sheet_timer.isActive():
            return True

        # Kiểm tra có worker đang chạy trong thread - bảo vệ khỏi lỗi RuntimeError
        try:
            if hasattr(self, 'processing_thread') and self.processing_thread:
                try:
                    return self.processing_thread.isRunning()
                except (RuntimeError, AttributeError):
                    # Thread đã bị xóa, bỏ qua
                    pass
        except Exception:
            pass

        return False

    def toggle_pause_resume(self):
        """Xử lý chức năng Tạm dừng/Tiếp tục"""
        if not hasattr(self, 'processing_worker') or not self.processing_worker:
            self.log("Không có tiến trình xử lý nào đang chạy.")
            self.pause_resume_btn.setEnabled(False)
            return

        # Kiểm tra trạng thái hiện tại của nút
        current_text = self.pause_resume_btn.text()

        if current_text == "Tạm dừng":
            # Đang xử lý, chuyển sang trạng thái tạm dừng
            self.pause_processing()
        else:
            # Đang tạm dừng, tiếp tục xử lý
            self.resume_processing()

    def pause_processing(self):
        """Tạm dừng quá trình xử lý và lưu checkpoint"""
        if not hasattr(self, 'processing_worker') or not self.processing_worker:
            return

        try:
            # Lấy thông tin vị trí hiện tại từ worker
            current_row = 0
            if hasattr(self.processing_worker, 'current_row'):
                current_row = self.processing_worker.current_row

            # Hủy worker hiện tại
            if hasattr(self.processing_worker, 'cancel'):
                self.processing_worker.cancel()
                self.log("Đã tạm dừng xử lý tại dòng " + str(current_row))

            # Lưu checkpoint
            self.checkpoint = {
                "active": True,
                "spreadsheet_id": getattr(self.spreadsheet, 'id', ""),
                "brand_sheet_name": self.brand_sheet_combo.currentText(),
                "deal_sheet_name": self.deal_sheet_combo.currentText(),
                "brand_code_col": self.brand_code_input.text().strip().upper(),
                "brand_type_col": self.brand_type_input.text().strip().upper(),
                "brand_name_col": self.brand_name_input.text().strip().upper(),
                "deal_brand_code_col": self.deal_brand_code_input.text().strip().upper(),
                "deal_product_col": self.deal_product_input.text().strip().upper(),
                "deal_price_col": self.deal_price_input.text().strip().upper(),
                "current_row": current_row,
                "model": self.model_combo.currentText()
            }

            # Lưu checkpoint vào file
            self.save_checkpoint()

            # Cập nhật UI
            self.pause_resume_btn.setText("Tiếp tục")
            self.process_btn.setEnabled(True)

            # Đảm bảo timer được dừng
            if hasattr(self, 'processing_timer') and self.processing_timer.isActive():
                self.processing_timer.stop()

        except Exception as e:
            self.log(f"Lỗi khi tạm dừng xử lý: {str(e)}")

    def resume_processing(self):
        """Tiếp tục xử lý từ checkpoint đã lưu"""
        if not self.checkpoint["active"]:
            self.log("Không có checkpoint để tiếp tục.")
            return

        try:
            # Kiểm tra spreadsheet
            if not self.spreadsheet or getattr(self.spreadsheet, 'id', "") != self.checkpoint["spreadsheet_id"]:
                # Cần tải lại spreadsheet
                self.log("Cần tải lại Google Sheet trước khi tiếp tục.")
                QMessageBox.information(self, "Thông báo", "Vui lòng tải lại Google Sheet trước khi tiếp tục xử lý.")
                return

            # Cập nhật UI từ checkpoint
            brand_index = self.brand_sheet_combo.findText(self.checkpoint["brand_sheet_name"])
            if brand_index >= 0:
                self.brand_sheet_combo.setCurrentIndex(brand_index)

            deal_index = self.deal_sheet_combo.findText(self.checkpoint["deal_sheet_name"])
            if deal_index >= 0:
                self.deal_sheet_combo.setCurrentIndex(deal_index)

            self.brand_code_input.setText(self.checkpoint["brand_code_col"])
            self.brand_type_input.setText(self.checkpoint["brand_type_col"])
            self.brand_name_input.setText(self.checkpoint["brand_name_col"])
            self.deal_brand_code_input.setText(self.checkpoint["deal_brand_code_col"])
            self.deal_product_input.setText(self.checkpoint["deal_product_col"])
            self.deal_price_input.setText(self.checkpoint["deal_price_col"])

            model_index = self.model_combo.findText(self.checkpoint["model"])
            if model_index >= 0:
                self.model_combo.setCurrentIndex(model_index)

            # Cập nhật dòng bắt đầu xử lý
            start_row = self.checkpoint["current_row"]
            self.last_row_input.setText(str(start_row))

            # Gọi hàm xử lý dữ liệu
            self.log(f"Tiếp tục xử lý từ dòng {start_row}...")
            self.start_processing()

            # Đặt lại trạng thái checkpoint
            self.checkpoint["active"] = False
            self.save_checkpoint()

            # Cập nhật UI
            self.pause_resume_btn.setText("Tạm dừng")

        except Exception as e:
            self.log(f"Lỗi khi tiếp tục xử lý: {str(e)}")

    def save_checkpoint(self):
        """Lưu checkpoint vào file"""
        try:
            checkpoint_path = DATA_DIR / "checkpoint.json"
            with open(checkpoint_path, "w", encoding="utf-8") as f:
                json.dump(self.checkpoint, f, ensure_ascii=False, indent=2)

            self.log("Đã lưu checkpoint.")
        except Exception as e:
            self.log(f"Lỗi khi lưu checkpoint: {str(e)}")

    def delete_checkpoint(self):
        try:
            checkpoint_path = DATA_DIR / "checkpoint.json"
            if checkpoint_path.exists():
                os.remove(checkpoint_path)
                self.log("Đã xóa checkpoint theo yêu cầu người dùng.")

            # Đặt lại biến checkpoint về trạng thái mặc định
            self.checkpoint = {
                "active": False,
                "spreadsheet_id": "",
                "brand_sheet_name": "",
                "deal_sheet_name": "",
                "brand_code_col": "A",
                "brand_type_col": "E",
                "brand_name_col": "D",
                "deal_brand_code_col": "A",
                "deal_product_col": "X",
                "deal_price_col": "AB",
                "model": "gpt-4o-mini",
                "current_row": 0
            }
        except Exception as e:
            self.log(f"Lỗi khi xóa checkpoint: {str(e)}")

    def load_checkpoint(self):
        try:
            checkpoint_path = DATA_DIR / "checkpoint.json"
            if checkpoint_path.exists():
                with open(checkpoint_path, "r", encoding="utf-8") as f:
                    self.checkpoint = json.load(f)

                if self.checkpoint.get("active", False):
                    self.log(f"Đã tìm thấy checkpoint tại dòng {self.checkpoint['current_row']}.")

                    # Hiển thị thông báo cho người dùng
                    reply = QMessageBox.question(
                        self,
                        "Tiếp tục xử lý?",
                        f"Đã tìm thấy một quá trình xử lý bị tạm dừng tại dòng {self.checkpoint['current_row']}.\n\nBạn có muốn tiếp tục xử lý không?",
                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                        QMessageBox.StandardButton.Yes
                    )

                    if reply == QMessageBox.StandardButton.Yes:
                        # Đặt sheet link để tải spreadsheet
                        sheet_id = self.checkpoint["spreadsheet_id"]
                        if sheet_id:
                            self.sheet_link_input.setText(f"https://docs.google.com/spreadsheets/d/{sheet_id}")
                            # Không gọi hàm load_sheet() ở đây vì cần đợi người dùng bấm nút Load Sheet trước
                    else:
                        # Người dùng chọn "Không" - xóa checkpoint để không hỏi lại lần sau
                        self.delete_checkpoint()

        except Exception as e:
            self.log(f"Lỗi khi tải checkpoint: {str(e)}")

# Thêm hàm định dạng số tiền
def format_price_vnd(price):
    """Format giá tiền theo định dạng VND"""
    return f"{price:,}".replace(",", ".")

def parse_price_vnd(price_str):
    """Chuyển đổi chuỗi giá VND thành số"""
    try:
        # Loại bỏ dấu chấm phân cách hàng nghìn
        cleaned = price_str.replace(".", "")
        return int(cleaned)
    except:
        return 0

def save_product_details(product_data):
    """Save detailed product information to database

    Args:
        product_data: A dictionary containing product details with keys:
            - product_info: Complete product name
            - brand: Brand name
            - product_name: Product name only
            - code: Product code
            - capacity: Product capacity/size
            - quantity: Quantity in set/combo
    """
    try:
        # Tạo bảng product_details nếu chưa tồn tại
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()

        # Tạo bảng product_details nếu chưa tồn tại
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS product_details (
            product_name TEXT PRIMARY KEY,
            brand TEXT,
            product_name_only TEXT,
            code TEXT,
            capacity TEXT,
            quantity INTEGER DEFAULT 1,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Extract data from the product_data dictionary
        product_name = product_data.get("product_info", "").strip()
        brand = product_data.get("brand", "").strip()
        product_name_only = product_data.get("product_name", "").strip()
        code = product_data.get("code", "").strip()
        capacity = product_data.get("capacity", "").strip()
        quantity = product_data.get("quantity", 1)

        # Ensure quantity is an integer
        try:
            quantity = int(quantity)
        except (ValueError, TypeError):
            quantity = 1

        if not product_name:  # Skip if product_name is empty
            print("Không thể lưu dữ liệu sản phẩm: Tên sản phẩm trống")
            return False

        # Insert or update product details
        cursor.execute('''
        INSERT OR REPLACE INTO product_details
        (product_name, brand, product_name_only, code, capacity, quantity)
        VALUES (?, ?, ?, ?, ?, ?)
        ''', (product_name, brand, product_name_only, code, capacity, quantity))

        conn.commit()
        conn.close()
        print(f"Đã lưu thông tin chi tiết sản phẩm: {product_name}")
        return True

    except Exception as e:
        print(f"Lỗi khi lưu thông tin sản phẩm: {e}")
        import traceback
        traceback.print_exc()
        return False

class PriceHistoryDialog(QDialog):
    """Dialog hiển thị lịch sử giá sản phẩm"""

    def __init__(self, product_name, parent=None):
        super().__init__(parent)
        self.product_name = product_name
        self.price_history, self.current_price = get_price_history(product_name)

        self.setWindowTitle(f"Lịch sử giá - {product_name}")
        self.setMinimumSize(800, 600)
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # Hiển thị giá hiện tại
        header_layout = QHBoxLayout()

        if not self.price_history and self.current_price > 0:
            # Nếu chưa có lịch sử giá, thêm giá hiện tại vào lịch sử
            current_date = datetime.now().strftime("%d.%m.%Y")
            # price, timestamp, live_session - 3 trường
            self.price_history.append((self.current_price, int(time.time()), current_date))

        current_price_label = QLabel(f"<h2>Giá hiện tại: <span style='color: #2196F3;'>{format_price_vnd(self.current_price)}đ</span></h2>")
        header_layout.addWidget(current_price_label)

        # Thêm nút cập nhật giá tự động
        auto_update_btn = QPushButton("Cập nhật giá tự động")
        auto_update_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        auto_update_btn.clicked.connect(self.show_auto_update_dialog)
        header_layout.addWidget(auto_update_btn, alignment=Qt.AlignmentFlag.AlignRight)

        layout.addLayout(header_layout)

        # Thông tin giá cao/thấp nhất
        if self.price_history:
            price_info_layout = QHBoxLayout()

            # Giá cao nhất - lưu ý đã giảm từ 4 trường xuống 3 trường
            max_price = max([price for price, _, _ in self.price_history])
            max_price_widget = QWidget()
            max_price_layout = QVBoxLayout(max_price_widget)
            max_price_layout.setContentsMargins(10, 10, 10, 10)
            max_price_label = QLabel(f"Giá cao nhất:")
            max_price_value = QLabel(f"<b>{format_price_vnd(max_price)}đ</b>")
            max_price_layout.addWidget(max_price_label)
            max_price_layout.addWidget(max_price_value)
            max_price_widget.setStyleSheet("background-color: #ff0000; color: white; border-radius: 10px;")

            # Giá thấp nhất - lưu ý đã giảm từ 4 trường xuống 3 trường
            min_price = min([price for price, _, _ in self.price_history])
            min_price_widget = QWidget()
            min_price_layout = QVBoxLayout(min_price_widget)
            min_price_layout.setContentsMargins(10, 10, 10, 10)
            min_price_label = QLabel(f"Giá thấp nhất:")
            min_price_value = QLabel(f"<b>{format_price_vnd(min_price)}đ</b>")
            min_price_layout.addWidget(min_price_label)
            min_price_layout.addWidget(min_price_value)
            min_price_widget.setStyleSheet("background-color: #00a800; color: white; border-radius: 10px;")

            price_info_layout.addWidget(max_price_widget)
            price_info_layout.addWidget(min_price_widget)
            layout.addLayout(price_info_layout)

            # Thêm widget để điều chỉnh giá
            self.add_price_editor(layout)

            # Tạo biểu đồ sử dụng phương thức mới
            chart_widget = self.create_chart(self.price_history, self.product_name)
            if chart_widget:
                layout.addWidget(chart_widget)

        # Buttons
        button_layout = QHBoxLayout()
        close_btn = QPushButton("Đóng")
        close_btn.clicked.connect(self.close)
        button_layout.addStretch()
        button_layout.addWidget(close_btn)
        layout.addLayout(button_layout)

        self.setLayout(layout)

    def show_auto_update_dialog(self):
        """Hiển thị dialog để cập nhật giá tự động từ spreadsheet"""
        # Lấy main window để truy cập spreadsheet
        main_window = None
        if hasattr(self, 'parent_widget'):
            main_window = self.parent_widget
        elif hasattr(self, 'parent') and callable(self.parent):
            parent = self.parent()
            if hasattr(parent, 'spreadsheet'):
                main_window = parent

        if not main_window or not hasattr(main_window, 'spreadsheet') or not main_window.spreadsheet:
            QMessageBox.warning(self, "Cảnh báo", "Bạn cần tải Google Sheet trước khi cập nhật giá tự động!")
            return

        # Dialog cấu hình
        config_dialog = QDialog(self)
        config_dialog.setWindowTitle("Cấu hình cập nhật giá")
        config_dialog.setFixedSize(400, 250)
        config_layout = QVBoxLayout(config_dialog)

        form_layout = QFormLayout()

        # Dropdown chọn sheet
        deal_sheet_combo = QComboBox()
        try:
            for sheet in main_window.spreadsheet.worksheets():
                deal_sheet_combo.addItem(sheet.title)

            # Chọn mặc định là Deal Sheet nếu có
            if hasattr(main_window, 'deal_sheet_combo'):
                current_deal_sheet = main_window.deal_sheet_combo.currentText()
                index = deal_sheet_combo.findText(current_deal_sheet)
                if index >= 0:
                    deal_sheet_combo.setCurrentIndex(index)
        except Exception as e:
            QMessageBox.warning(self, "Lỗi", f"Không thể lấy danh sách sheets: {str(e)}")
            return

        form_layout.addRow("Deal Sheet:", deal_sheet_combo)

        # Cột sản phẩm
        product_col_input = QLineEdit()
        if hasattr(main_window, 'deal_product_input'):
            product_col_input.setText(main_window.deal_product_input.text())
        else:
            product_col_input.setText("H")  # Mặc định là cột H
        form_layout.addRow("Cột sản phẩm:", product_col_input)

        # Cột giá
        price_col_input = QLineEdit()
        if hasattr(main_window, 'deal_price_input'):
            price_col_input.setText(main_window.deal_price_input.text())
        else:
            price_col_input.setText("AB")  # Mặc định là cột AB
        form_layout.addRow("Cột giá:", price_col_input)

        # Phiên Live
        live_session_date = QDateEdit()
        live_session_date.setCalendarPopup(True)
        live_session_date.setDate(QDate.currentDate())
        live_session_date.setDisplayFormat("dd.MM")
        live_session_date.setToolTip("Chọn ngày của phiên live (định dạng ngày.tháng)")
        form_layout.addRow("Phiên Live:", live_session_date)

        config_layout.addLayout(form_layout)

        # Thêm ghi chú
        note_label = QLabel("Lưu ý: Quá trình này sẽ quét toàn bộ Deal list và cập nhật giá cho tất cả sản phẩm có trong database nếu phát hiện sự thay đổi giá > 5% hoặc > 10,000đ.")
        note_label.setWordWrap(True)
        note_label.setStyleSheet("color: #555; font-style: italic;")
        config_layout.addWidget(note_label)

        button_box = QHBoxLayout()
        cancel_btn = QPushButton("Hủy")
        cancel_btn.clicked.connect(config_dialog.reject)
        start_btn = QPushButton("Bắt đầu cập nhật")
        start_btn.clicked.connect(config_dialog.accept)
        start_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")

        button_box.addWidget(cancel_btn)
        button_box.addWidget(start_btn)
        config_layout.addLayout(button_box)

        # Hiển thị dialog
        if config_dialog.exec() == QDialog.DialogCode.Accepted:
            # Người dùng đã xác nhận, bắt đầu cập nhật
            deal_sheet = deal_sheet_combo.currentText()
            product_col = product_col_input.text().strip().upper()
            price_col = price_col_input.text().strip().upper()

            # Lấy phiên live từ input
            date = live_session_date.date()
            live_session = f"{date.day()}.{date.month()}"

            # Tạo dialog tiến trình
            progress_dialog = QDialog(self)
            progress_dialog.setWindowTitle("Đang cập nhật giá...")
            progress_dialog.setFixedSize(500, 400)
            progress_layout = QVBoxLayout(progress_dialog)

            # Thêm progress bar
            progress_label = QLabel("Đang quét dữ liệu...")
            progress_layout.addWidget(progress_label)

            progress_bar = QProgressBar()
            progress_bar.setRange(0, 100)
            progress_bar.setValue(0)
            progress_layout.addWidget(progress_bar)

            # Thêm log text area
            log_text = QTextEdit()
            log_text.setReadOnly(True)
            log_text.setMinimumHeight(200)
            progress_layout.addWidget(log_text)

            # Nút hủy
            cancel_btn = QPushButton("Hủy")
            cancel_btn.clicked.connect(progress_dialog.reject)
            progress_layout.addWidget(cancel_btn, alignment=Qt.AlignmentFlag.AlignCenter)

            # Hiển thị dialog và bắt đầu cập nhật
            progress_dialog.show()

            # Setup callbacks
            def update_progress(value):
                progress_bar.setValue(value)
                QApplication.processEvents()

            def log_message(message):
                log_text.append(message)
                # Bỏ tự động cuộn xuống
                # log_text.verticalScrollBar().setValue(log_text.verticalScrollBar().maximum())
                QApplication.processEvents()

            # Chạy trong thread riêng để không block UI
            class UpdateWorker(QObject):
                finished = pyqtSignal(dict)

                def __init__(self, spreadsheet, deal_sheet, product_col, price_col, live_session):
                    super().__init__()
                    self.spreadsheet = spreadsheet
                    self.deal_sheet = deal_sheet
                    self.product_col = product_col
                    self.price_col = price_col
                    self.live_session = live_session

                def run(self):
                    result = auto_update_prices(
                        self.spreadsheet,
                        self.deal_sheet,
                        self.product_col,
                        self.price_col,
                        log_message,
                        update_progress,
                        self.live_session
                    )
                    self.finished.emit(result)

            # Tạo worker và thread
            worker = UpdateWorker(main_window.spreadsheet, deal_sheet, product_col, price_col, live_session)
            thread = QThread()
            worker.moveToThread(thread)

            # Kết nối tín hiệu
            thread.started.connect(worker.run)
            worker.finished.connect(lambda result: self.show_update_results(result, progress_dialog))
            worker.finished.connect(thread.quit)
            worker.finished.connect(worker.deleteLater)
            thread.finished.connect(thread.deleteLater)

            # Bắt đầu thread
            thread.start()

    def show_update_results(self, result, progress_dialog=None):
        """Hiển thị kết quả cập nhật giá"""
        if progress_dialog:
            progress_dialog.accept()  # Đóng dialog tiến trình

        # Kiểm tra lỗi
        if "error" in result:
            QMessageBox.warning(self, "Lỗi cập nhật",
                               f"Đã xảy ra lỗi khi cập nhật giá: {result['error']}")
            return

        # Hiển thị dialog kết quả
        result_dialog = AutoPriceUpdateDialog(result, self)
        result_dialog.exec()

        # Nếu đang xem chi tiết một sản phẩm và giá của nó đã được cập nhật,
        # reload dialog để hiển thị giá mới
        if result.get("updated", 0) > 0:
            updated_products = [p["name"] for p in result.get("products", [])]
            if self.product_name in updated_products:
                self.close()
                dialog = PriceHistoryDialog(self.product_name, self.parent())
                dialog.exec()

    # Các phương thức khác giữ nguyên
    def add_price_editor(self, layout):
        """Thêm widget để chỉnh sửa giá sản phẩm"""
        edit_group = QGroupBox("Cập nhật giá mới")
        edit_layout = QGridLayout()

        # Input giá mới
        price_label = QLabel("Giá mới:")
        self.price_edit = QLineEdit()
        self.price_edit.setPlaceholderText("Nhập giá mới (VND)")

        # Input ngày
        date_label = QLabel("Ngày hiệu lực:")
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setDisplayFormat("dd.MM.yyyy")

        # Nút lưu
        save_btn = QPushButton("Lưu giá mới")
        save_btn.clicked.connect(self.save_new_price)

        # Thêm vào layout
        edit_layout.addWidget(price_label, 0, 0)
        edit_layout.addWidget(self.price_edit, 0, 1)
        edit_layout.addWidget(date_label, 1, 0)
        edit_layout.addWidget(self.date_edit, 1, 1)
        edit_layout.addWidget(save_btn, 2, 0, 1, 2)

        edit_group.setLayout(edit_layout)
        layout.addWidget(edit_group)

    def create_chart(self, price_history, product_name):
        """Tạo biểu đồ lịch sử giá"""
        try:
            # Nếu không có dữ liệu, trả về None
            if not price_history:
                logger.warning("Không có dữ liệu lịch sử giá để hiển thị")
                return None

            # Chuẩn bị dữ liệu
            dates = []
            prices = []
            sessions = []
            tooltips = []

            # Phân tích dữ liệu giá
            for record in price_history:
                try:
                    # Kiểm tra cấu trúc của record và xử lý theo đúng cấu trúc
                    # Thử lấy giá từ các vị trí khác nhau và chuyển đổi an toàn
                    price_value = None
                    session = ""
                    timestamp = ""

                    # Xác định vị trí của giá sản phẩm
                    if len(record) >= 1:
                        try:
                            # Thử lấy từ vị trí 0 (mặc định theo get_price_history)
                            price_value = int(record[0])
                        except (ValueError, TypeError):
                            # Nếu không thành công, thử vị trí khác
                            try:
                                if len(record) >= 2:
                                    # Thử lấy từ vị trí 1
                                    price_value = int(record[1])
                            except (ValueError, TypeError):
                                # Nếu vẫn không thành công, bỏ qua record này
                                logger.error(f"Không thể chuyển đổi bất kỳ giá trị nào trong record thành giá: {record}")
                                continue

                    # Nếu không tìm được giá, bỏ qua record này
                    if price_value is None:
                        logger.error(f"Không thể xác định giá từ record: {record}")
                        continue

                    prices.append(price_value)

                    # Xác định vị trí của thông tin session và timestamp
                    if len(record) >= 3:
                        # Nếu record có ít nhất 3 phần tử
                        # Thử lấy session và timestamp theo vị trí dự kiến
                        session = str(record[2]) if record[2] is not None else ""
                        if len(record) >= 4:
                            timestamp = str(record[3]) if record[3] is not None else ""
                        else:
                            timestamp = str(record[1]) if record[1] is not None else ""
                    elif len(record) >= 2:
                        # Nếu record chỉ có 2 phần tử
                        session = str(record[1]) if record[1] is not None else ""

                    # Bảo đảm session luôn có giá trị
                    if not session:
                        session = "Phiên không xác định"

                    sessions.append(session)

                    # Định dạng tooltip với thông tin đầy đủ
                    tooltip = f"Giá: {price_value:,.0f}đ\nPhiên: {session}\nThời gian: {timestamp}"
                    tooltips.append(tooltip)
                except (ValueError, IndexError) as e:
                    logger.error(f"Lỗi khi xử lý dữ liệu giá: {e}")
                    logger.error(f"Chi tiết record lỗi: {record}")
                    continue

            # Kiểm tra lại nếu có dữ liệu sau khi xử lý
            if not prices:
                logger.warning("Không có dữ liệu giá hợp lệ để tạo biểu đồ")
                return None

            # Tạo biểu đồ bằng matplotlib
            fig, ax = plt.subplots(figsize=(10, 6))

            # Vẽ đường giá với marker
            ax.plot(sessions, prices, marker='o', linestyle='-', color='blue', markersize=8)

            # Đánh dấu giá cao nhất và thấp nhất
            max_price = max(prices)
            min_price = min(prices)
            max_idx = prices.index(max_price)
            min_idx = prices.index(min_price)

            # Ghi chú giá cao nhất
            ax.annotate(f'{max_price:,.0f}đ',
                        xy=(max_idx, max_price),
                        xytext=(0, 15),
                        textcoords='offset points',
                        ha='center',
                        va='bottom',
                        bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.7))

            # Ghi chú giá thấp nhất
            ax.annotate(f'{min_price:,.0f}đ',
                        xy=(min_idx, min_price),
                        xytext=(0, -15),
                        textcoords='offset points',
                        ha='center',
                        va='top',
                        bbox=dict(boxstyle='round,pad=0.5', fc='lightblue', alpha=0.7))

            # Phân tích xu hướng
            trend_text = "Xu hướng: "
            if len(prices) > 1:
                if prices[0] > prices[-1]:
                    trend_text += "Giảm"
                elif prices[0] < prices[-1]:
                    trend_text += "Tăng"
                else:
                    trend_text += "Ổn định"
            else:
                trend_text += "Chưa xác định"

            # Thiết lập tiêu đề và nhãn trục
            ax.set_title(f'Biểu đồ lịch sử giá: {product_name}\n{trend_text}')
            ax.set_xlabel('Phiên')
            ax.set_ylabel('Giá (VNĐ)')

            # Định dạng trục y để hiển thị giá
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, loc: f"{int(x):,}"))

            # Xoay nhãn trục x để dễ đọc
            plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

            # Điều chỉnh layout
            plt.tight_layout()

            # Lưu biểu đồ vào file tạm
            import tempfile
            import os
            from PyQt6.QtGui import QPixmap
            from PyQt6.QtWidgets import QLabel

            temp_dir = tempfile.gettempdir()
            temp_file = os.path.join(temp_dir, f"price_chart_{int(time.time())}.png")
            plt.savefig(temp_file, dpi=100, bbox_inches='tight')
            plt.close(fig)

            # Tạo widget chứa biểu đồ từ file ảnh
            pixmap = QPixmap(temp_file)
            label = QLabel()
            label.setPixmap(pixmap)
            label.setScaledContents(True)

            return label

        except Exception as e:
            logger.error(f"Lỗi khi tạo biểu đồ: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return None

    def save_new_price(self):
        """Lưu giá mới vào database"""
        try:
            price_text = self.price_edit.text()
            if not price_text:
                QMessageBox.warning(self, "Lỗi", "Vui lòng nhập giá.")
                return

            # Chuyển đổi giá
            try:
                # Xử lý cả trường hợp có dấu phân cách hàng nghìn
                price_text = price_text.replace(".", "").replace(",", "")
                new_price = int(price_text)
            except ValueError:
                QMessageBox.warning(self, "Lỗi", "Giá không hợp lệ. Vui lòng nhập số.")
                return

            # Lấy ngày
            date = self.date_edit.date()
            price_date = f"{date.day()}.{date.month()}.{date.year()}"

            # Cập nhật giá
            update_product_price(self.product_name, new_price, price_date)

            # Làm mới dữ liệu
            self.price_history, self.current_price = get_price_history(self.product_name)

            # Làm mới UI
            QMessageBox.information(self, "Thành công", "Đã cập nhật giá mới.")
            self.close()
            dialog = PriceHistoryDialog(self.product_name, self.parent())
            dialog.exec()

        except Exception as e:
            QMessageBox.warning(self, "Lỗi", f"Không thể cập nhật giá: {str(e)}")

class AutoPriceUpdateDialog(QDialog):
    """Dialog hiển thị kết quả cập nhật giá tự động"""

    def __init__(self, result_data, parent=None):
        super().__init__(parent)
        self.result_data = result_data
        self.setWindowTitle("Kết quả cập nhật giá tự động")
        self.setMinimumSize(800, 500)
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)


        # Hiển thị thông tin tổng quan
        scanned = self.result_data.get("scanned", 0)
        updated = self.result_data.get("updated", 0)
        products = self.result_data.get("products", [])
        live_session = self.result_data.get("live_session", "")
        spreadsheet_name = self.result_data.get("spreadsheet_name", "")

        # Xử lý tên spreadsheet
        # Chỉ lấy phần trước "Internal" trong tên spreadsheet
        if "Internal" in spreadsheet_name:
            spreadsheet_name = spreadsheet_name.split("Internal")[0].strip()

        # Loại bỏ dấu [] nếu có
        spreadsheet_name = re.sub(r'\[|\]', '', spreadsheet_name).strip()

        info_widget = QWidget()
        info_widget.setStyleSheet("background-color: #e3f2fd; border-radius: 10px; padding: 10px;")
        info_layout = QVBoxLayout(info_widget)

        # Thêm biểu tượng và tiêu đề
        title_layout = QHBoxLayout()
        title_label = QLabel(f"<h3>Cập nhật giá hoàn tất</h3>")
        title_layout.addWidget(title_label)
        info_layout.addLayout(title_layout)

        # Hiển thị thông tin phiên Live
        if live_session:
            live_session_display = f"{live_session}"
            if spreadsheet_name:
                live_session_display = f"{live_session} - {spreadsheet_name}"

            live_label = QLabel(f"<b>Phiên Live:</b> Deal list - {spreadsheet_name}")
            info_layout.addWidget(live_label)

        # Thêm thông tin chi tiết
        details_layout = QHBoxLayout()

        scanned_widget = QWidget()
        scanned_layout = QVBoxLayout(scanned_widget)
        scanned_layout.setContentsMargins(10, 10, 10, 10)
        scanned_label = QLabel(f"Đã quét:")
        scanned_value = QLabel(f"<b>{scanned}</b> sản phẩm")
        scanned_layout.addWidget(scanned_label)
        scanned_layout.addWidget(scanned_value)

        updated_widget = QWidget()
        updated_layout = QVBoxLayout(updated_widget)
        updated_layout.setContentsMargins(10, 10, 10, 10)
        updated_label = QLabel(f"Đã cập nhật:")
        updated_value = QLabel(f"<b>{updated}</b> sản phẩm")
        updated_layout.addWidget(updated_label)
        updated_layout.addWidget(updated_value)

        details_layout.addWidget(scanned_widget)
        details_layout.addWidget(updated_widget)
        info_layout.addLayout(details_layout)

        layout.addWidget(info_widget)

        # Bảng sản phẩm đã cập nhật
        if products:
            group_box = QGroupBox("Danh sách sản phẩm đã cập nhật")
            group_layout = QVBoxLayout()

            # Tạo bảng
            table = QTableWidget()
            table.setColumnCount(6)  # Thêm cột phiên live
            table.setHorizontalHeaderLabels(["Sản phẩm", "Giá cũ", "Giá mới", "Thay đổi", "Tỷ lệ", "Phiên Live"])
            table.setRowCount(len(products))

            table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
            table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)
            table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)
            table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)
            table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)
            table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)

            table.setColumnWidth(1, 120)
            table.setColumnWidth(2, 120)
            table.setColumnWidth(3, 120)
            table.setColumnWidth(4, 80)
            table.setColumnWidth(5, 80)

            for i, product in enumerate(products):
                # Tên sản phẩm
                table.setItem(i, 0, QTableWidgetItem(product["name"]))

                # Giá cũ
                old_price_item = QTableWidgetItem(format_price_vnd(product["old_price"]) + "đ")
                old_price_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                table.setItem(i, 1, old_price_item)

                # Giá mới
                new_price_item = QTableWidgetItem(format_price_vnd(product["new_price"]) + "đ")
                new_price_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                table.setItem(i, 2, new_price_item)

                # Chênh lệch
                price_diff = product["new_price"] - product["old_price"]
                diff_text = f"{'+' if price_diff > 0 else ''}{format_price_vnd(price_diff)}đ"
                diff_item = QTableWidgetItem(diff_text)
                diff_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)

                # Màu nền phụ thuộc vào chênh lệch
                if price_diff > 0:
                    diff_item.setBackground(QColor(255, 235, 238))  # Đỏ nhạt
                elif price_diff < 0:
                    diff_item.setBackground(QColor(232, 245, 233))  # Xanh nhạt

                table.setItem(i, 3, diff_item)

                # Tỷ lệ thay đổi
                percent_change = product["change_percent"]
                percent_item = QTableWidgetItem(f"{'+' if price_diff > 0 else ''}{percent_change:.1f}%")
                percent_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
                table.setItem(i, 4, percent_item)

                # Phiên live
                live_item = QTableWidgetItem(product.get("live_session", live_session))
                live_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignVCenter)
                table.setItem(i, 5, live_item)

            group_layout.addWidget(table)
            group_box.setLayout(group_layout)
            layout.addWidget(group_box)
        else:
            # Không có sản phẩm nào được cập nhật
            no_update_label = QLabel("Không có sản phẩm nào cần cập nhật giá.")
            no_update_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            no_update_label.setStyleSheet("color: #666; margin: 20px;")
            layout.addWidget(no_update_label)

        # Thêm nút xuất báo cáo và đóng
        button_layout = QHBoxLayout()
        export_btn = QPushButton("Xuất báo cáo")
        export_btn.clicked.connect(self.export_report)

        close_btn = QPushButton("Đóng")
        close_btn.clicked.connect(self.accept)

        button_layout.addWidget(export_btn)
        button_layout.addStretch()
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)

    def export_report(self):
        """Xuất báo cáo cập nhật giá ra file CSV"""
        try:
            from datetime import datetime
            import csv
            import os

            # Lấy thông tin phiên live cho tên file
            live_session = self.result_data.get("live_session", "").replace(".", "_")
            filename = f"price_update_report_{live_session or datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

            # Chọn đường dẫn lưu file
            file_path, _ = QFileDialog.getSaveFileName(
                        self,
                "Lưu báo cáo",
                os.path.join(os.path.expanduser("~"), filename),
                "CSV Files (*.csv)"
            )

            if not file_path:
                return

            # Chuẩn bị dữ liệu
            products = self.result_data.get("products", [])
            if not products:
                QMessageBox.information(self, "Thông báo", "Không có dữ liệu để xuất báo cáo.")
                return

            # Lấy thông tin phiên Live
            live_session = self.result_data.get("live_session", "")
            spreadsheet_name = self.result_data.get("spreadsheet_name", "")

            # Ghi file CSV
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                # Thêm thông tin phiên Live
                writer.writerow(["Thông tin báo cáo"])
                writer.writerow(["Phiên Live", live_session])
                writer.writerow(["Spreadsheet", spreadsheet_name])
                writer.writerow(["Thời gian xuất", datetime.now().strftime("%d/%m/%Y %H:%M:%S")])
                writer.writerow([])  # Dòng trống

                # Tiêu đề
                writer.writerow(["Sản phẩm", "Giá cũ", "Giá mới", "Chênh lệch", "Tỷ lệ thay đổi (%)", "Phiên Live"])

                # Dữ liệu
                for product in products:
                    writer.writerow([
                        product["name"],
                        product["old_price"],
                        product["new_price"],
                        product["new_price"] - product["old_price"],
                        round(product["change_percent"], 2),
                        product.get("live_session", live_session)
                    ])

            QMessageBox.information(self, "Thành công", f"Đã xuất báo cáo thành công đến:\n{file_path}")

        except Exception as e:
            QMessageBox.warning(self, "Lỗi xuất báo cáo", f"Đã xảy ra lỗi khi xuất báo cáo: {str(e)}")

class DatabaseManagerDialog(QDialog):
    """Dialog để quản lý cơ sở dữ liệu"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Quản lý cơ sở dữ liệu")
        self.setMinimumSize(900, 600)
        self.parent_widget = parent

        # Kiểm tra và tạo database nếu cần
        try:
            self.check_database_tables()
        except Exception as e:
            QMessageBox.critical(self, "Lỗi database", f"Không thể kết nối hoặc kiểm tra database: {e}")

        self.setup_ui()

    # Thêm phương thức result_add_log
    def result_add_log(self, message):
        """Ghi log ra console và không chuyển đến widget cha"""
        print(f"DatabaseManagerDialog: {message}")
        # Đã loại bỏ việc chuyển log đến widget cha

    def check_database_tables(self):
        """Kiểm tra các bảng trong database"""
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()

        # Kiểm tra bảng product_classifications
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='product_classifications'")
        product_table_exists = cursor.fetchone() is not None

        # Kiểm tra bảng price_history
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='price_history'")
        price_table_exists = cursor.fetchone() is not None

        # Kiểm tra bảng brands
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='brands'")
        brand_table_exists = cursor.fetchone() is not None

        # Kiểm tra bảng feedback
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='feedback'")
        feedback_table_exists = cursor.fetchone() is not None

        # Kiểm tra bảng similar_products
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='similar_products'")
        similar_table_exists = cursor.fetchone() is not None

        # Kiểm tra bảng product_details
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='product_details'")
        details_table_exists = cursor.fetchone() is not None

        # Kiểm tra bảng settings
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='settings'")
        settings_table_exists = cursor.fetchone() is not None

        # Kiểm tra bảng sync_status
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='sync_status'")
        sync_status_table_exists = cursor.fetchone() is not None

        # Nếu không có bảng nào, tạo database
        if not (product_table_exists and price_table_exists and brand_table_exists and
                feedback_table_exists and similar_table_exists and details_table_exists and
                settings_table_exists and sync_status_table_exists):
            setup_database()
            check_and_update_table_structure()

        # Kiểm tra và tạo bảng product_details nếu cần
        if not details_table_exists:
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS product_details (
                product_name TEXT PRIMARY KEY,
                brand TEXT,
                product_name_only TEXT,
                code TEXT,
                capacity TEXT,
                quantity INTEGER DEFAULT 1,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            conn.commit()
            print("Đã tạo bảng product_details")

        conn.close()

        self.product_table_exists = product_table_exists
        self.price_table_exists = price_table_exists
        self.brand_table_exists = brand_table_exists
        self.feedback_table_exists = feedback_table_exists
        self.similar_table_exists = similar_table_exists
        self.details_table_exists = details_table_exists
        self.settings_table_exists = settings_table_exists
        self.sync_status_table_exists = sync_status_table_exists

    def is_table_available(self, table_name):
        """Kiểm tra một bảng có tồn tại không"""
        return getattr(self, f"{table_name}_table_exists", False)

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # Thêm tiêu đề
        title_label = QLabel("Quản lý cơ sở dữ liệu")
        title_label.setStyleSheet("font-size: 18pt; font-weight: bold;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # Debug thông tin bảng
        self.debug_info()

        # Tạo tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)

        # Tab sản phẩm
        self.create_product_tab()

        # Tab lịch sử giá
        self.create_price_history_tab()

        # Tab thương hiệu
        self.create_brand_tab()

        # Nút làm mới và đóng
        button_layout = QHBoxLayout()

        # Nút cập nhật giá tự động
        auto_update_btn = QPushButton("Cập nhật giá tự động từ Deal list")
        auto_update_btn.clicked.connect(self.start_auto_price_update)
        button_layout.addWidget(auto_update_btn)

        button_layout.addStretch()

        refresh_btn = QPushButton("Làm mới dữ liệu")
        refresh_btn.clicked.connect(self.refresh_data)
        button_layout.addWidget(refresh_btn)

        close_btn = QPushButton("Đóng")
        close_btn.clicked.connect(self.close)
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)

        # Load dữ liệu
        self.refresh_data()

    def debug_info(self):
        """Hiển thị thông tin debug về trạng thái bảng trong database"""
        table_info = {
            "product_classifications": self.product_table_exists,
            "price_history": self.price_table_exists,
            "brands": self.brand_table_exists,
            "feedback": self.feedback_table_exists,
            "similar_products": self.similar_table_exists,
            "product_details": self.details_table_exists,
            "settings": self.settings_table_exists,
            "sync_status": self.sync_status_table_exists
        }

        for table_name, exists in table_info.items():
            status = "Tồn tại" if exists else "Không tồn tại"
            self.result_add_log(f"Bảng {table_name}: {status}")
            print(f"Bảng {table_name}: {status}")

        # Kiểm tra số lượng dữ liệu trong các bảng
        try:
            conn = sqlite3.connect(str(DB_PATH))
            cursor = conn.cursor()

            tables = ["product_classifications", "price_history", "brands", "product_details"]
            for table in tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    self.result_add_log(f"Số lượng bản ghi trong bảng {table}: {count}")
                    print(f"Số lượng bản ghi trong bảng {table}: {count}")

                    # Kiểm tra và tạo dữ liệu mẫu cho bảng brands nếu trống
                    if table == "brands" and count == 0:
                        self.check_and_create_brands()
                except Exception as e:
                    self.result_add_log(f"Lỗi đếm bản ghi trong bảng {table}: {str(e)}")
                    print(f"Lỗi đếm bản ghi trong bảng {table}: {str(e)}")

            # Kiểm tra các Brand có trong bảng brands
            try:
                cursor.execute("SELECT brand_code, brand_name FROM brands LIMIT 10")
                brands = cursor.fetchall()
                if brands:
                    self.result_add_log("Danh sách 10 thương hiệu đầu tiên:")
                    print("Danh sách 10 thương hiệu đầu tiên:")
                    for brand_code, brand_name in brands:
                        self.result_add_log(f"  - {brand_code}: {brand_name}")
                        print(f"  - {brand_code}: {brand_name}")
                else:
                    self.result_add_log("Không có thương hiệu nào trong database")
                    print("Không có thương hiệu nào trong database")
            except Exception as e:
                self.result_add_log(f"Lỗi kiểm tra thương hiệu: {str(e)}")
                print(f"Lỗi kiểm tra thương hiệu: {str(e)}")

            conn.close()
        except Exception as e:
            self.result_add_log(f"Lỗi kết nối database để debug: {str(e)}")
            print(f"Lỗi kết nối database để debug: {str(e)}")

        # Kiểm tra Google Sheets và Brand List
        self.check_google_sheets_brand_list()

    def check_google_sheets_brand_list(self):
        """Kiểm tra thông tin Brand list từ Google Sheets và cập nhật vào database"""
        self.result_add_log("Kiểm tra Brand list từ Google Sheets...")

        # Kiểm tra xem parent_widget có tồn tại và có spreadsheet không
        if not hasattr(self, 'parent_widget') or not hasattr(self.parent_widget, 'spreadsheet') or not self.parent_widget.spreadsheet:
            self.result_add_log("Không tìm thấy Google Sheets! Vui lòng tải Google Sheet trước.")
            return

        # Lấy spreadsheet từ parent widget
        spreadsheet = self.parent_widget.spreadsheet

        try:
            # Kiểm tra các sheet có trong spreadsheet
            sheet_names = [worksheet.title for worksheet in spreadsheet.worksheets()]
            self.result_add_log(f"Danh sách sheet: {', '.join(sheet_names)}")

            # Tìm sheet Brand list
            brand_sheet_name = None
            for name in sheet_names:
                if "brand" in name.lower():
                    brand_sheet_name = name
                    break

            if not brand_sheet_name:
                self.result_add_log("Không tìm thấy sheet chứa Brand list.")
                return

            self.result_add_log(f"Đã tìm thấy sheet Brand list: {brand_sheet_name}")

            # Lấy dữ liệu từ Brand list
            brand_sheet = spreadsheet.worksheet(brand_sheet_name)
            all_data = brand_sheet.get_all_values()

            if len(all_data) < 2:  # Kiểm tra có ít nhất 1 dòng dữ liệu + tiêu đề
                self.result_add_log("Brand list không có dữ liệu.")
                return

            # Xác định cột Brand code và Brand name
            headers = all_data[0]
            brand_code_col = None
            brand_name_col = None

            for i, header in enumerate(headers):
                header_lower = header.lower()
                if "code" in header_lower or "mã" in header_lower:
                    brand_code_col = i
                elif "name" in header_lower or "tên" in header_lower:
                    brand_name_col = i

            if brand_code_col is None or brand_name_col is None:
                self.result_add_log("Không tìm thấy cột Brand code hoặc Brand name trong Brand list.")
                return

            self.result_add_log(f"Đã xác định cột: Brand code (cột {chr(65+brand_code_col)}), Brand name (cột {chr(65+brand_name_col)})")

            # Lấy dữ liệu Brand code và Brand name
            brand_data = []
            for row in all_data[1:]:  # Bỏ qua dòng tiêu đề
                if len(row) > max(brand_code_col, brand_name_col):
                    code = row[brand_code_col].strip()
                    name = row[brand_name_col].strip()
                    if code and name:
                        brand_data.append((code, name))

            # Hiển thị 5 Brand đầu tiên
            self.result_add_log(f"Đã tìm thấy {len(brand_data)} Brand trong sheet.")
            if brand_data:
                self.result_add_log("5 Brand đầu tiên:")
                for i, (code, name) in enumerate(brand_data[:5]):
                    self.result_add_log(f"  {i+1}. {code}: {name}")

                # Đồng bộ dữ liệu Brand list với database
                conn = sqlite3.connect(str(DB_PATH))
                cursor = conn.cursor()

                # Lấy tất cả brand_code đã có trong database
                cursor.execute("SELECT brand_code, classification FROM brands")
                existing_brands = {row[0]: row[1] for row in cursor.fetchall()}

                # Đếm số lượng brand đã thêm mới và cập nhật
                new_brands = 0
                updated_brands = 0

                for code, name in brand_data:
                    if code in existing_brands:
                        # Brand đã tồn tại, cập nhật tên nếu cần nhưng giữ nguyên phân loại
                        classification = existing_brands[code]
                        cursor.execute(
                            "UPDATE brands SET brand_name = ? WHERE brand_code = ?",
                            (name, code)
                        )
                        updated_brands += 1
                    else:
                        # Brand mới, thêm vào database với classification trống
                        cursor.execute(
                            "INSERT INTO brands (brand_code, brand_name, classification) VALUES (?, ?, ?)",
                            (code, name, "")
                        )
                        new_brands += 1

                conn.commit()

                if new_brands > 0 or updated_brands > 0:
                    self.result_add_log(f"Đã thêm {new_brands} brand mới và cập nhật {updated_brands} brand hiện có.")
                else:
                    self.result_add_log("Không có brand nào cần cập nhật.")

                conn.close()

        except Exception as e:
            self.result_add_log(f"Lỗi khi kiểm tra Brand list từ Google Sheets: {str(e)}")
            print(f"Lỗi chi tiết: {str(e)}")

    def check_and_create_brands(self):
        """Kiểm tra bảng brands"""
        try:
            conn = sqlite3.connect(str(DB_PATH))
            cursor = conn.cursor()

            # Kiểm tra số lượng bản ghi trong bảng brands
            cursor.execute("SELECT COUNT(*) FROM brands")
            count = cursor.fetchone()[0]

            if count == 0:
                self.result_add_log("Bảng brands trống.")

            conn.close()
        except Exception as e:
            self.result_add_log(f"Lỗi khi kiểm tra bảng brands: {str(e)}")
            print(f"Lỗi khi kiểm tra bảng brands: {str(e)}")

    def create_product_tab(self):
        """Tạo tab hiển thị dữ liệu sản phẩm"""
        product_tab = QWidget()
        layout = QVBoxLayout(product_tab)

        # Tạo bộ lọc
        filter_layout = QHBoxLayout()
        filter_label = QLabel("Lọc:")
        self.product_filter = QLineEdit()
        self.product_filter.setPlaceholderText("Nhập từ khóa để lọc...")
        self.product_filter.textChanged.connect(self.filter_products)
        filter_layout.addWidget(filter_label)
        filter_layout.addWidget(self.product_filter)
        layout.addLayout(filter_layout)

        # Tạo bảng dữ liệu
        self.product_table = QTableWidget()
        self.product_table.setColumnCount(8)
        self.product_table.setHorizontalHeaderLabels([
            "Tên sản phẩm", "Thương hiệu", "Phân loại", "Tên riêng",
            "Mã", "Dung tích", "Số lượng", "Giá gần nhất"
        ])
        self.product_table.horizontalHeader().setStretchLastSection(True)
        self.product_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.product_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.product_table.setSortingEnabled(True)
        self.product_table.setAlternatingRowColors(True)

        # Thiết lập độ rộng cột
        self.product_table.setColumnWidth(0, 250)  # Tên sản phẩm
        self.product_table.setColumnWidth(1, 100)  # Thương hiệu
        self.product_table.setColumnWidth(2, 100)  # Phân loại
        self.product_table.setColumnWidth(3, 150)  # Tên riêng

        layout.addWidget(self.product_table)

        # Nút để xem lịch sử giá của sản phẩm được chọn
        btn_layout = QHBoxLayout()
        view_history_btn = QPushButton("Xem lịch sử giá")
        view_history_btn.clicked.connect(self.show_selected_price_history)

        btn_layout.addStretch()
        btn_layout.addWidget(view_history_btn)
        layout.addLayout(btn_layout)

        self.tab_widget.addTab(product_tab, "Sản phẩm")

        # Kết nối sự kiện khi người dùng click đúp vào một sản phẩm
        self.product_table.doubleClicked.connect(self.show_product_price_history)

    def filter_products(self):
        """Lọc danh sách sản phẩm dựa trên từ khóa tìm kiếm"""
        try:
            search_text = self.product_filter.text().lower()
            for row in range(self.product_table.rowCount()):
                match = False
                for col in range(self.product_table.columnCount()):
                    item = self.product_table.item(row, col)
                    if item and search_text in item.text().lower():
                        match = True
                        break
                self.product_table.setRowHidden(row, not match)
        except Exception as e:
            print(f"Lỗi khi lọc sản phẩm: {str(e)}")
            QMessageBox.warning(self, "Lỗi lọc dữ liệu", f"Đã xảy ra lỗi khi lọc dữ liệu: {str(e)}")

    def filter_prices(self):
        """Lọc danh sách lịch sử giá dựa trên từ khóa tìm kiếm"""
        try:
            search_text = self.price_search.text().lower()
            for row in range(self.price_table.rowCount()):
                match = False
                for col in range(self.price_table.columnCount()):
                    item = self.price_table.item(row, col)
                    if item and search_text in item.text().lower():
                        match = True
                        break
                self.price_table.setRowHidden(row, not match)
        except Exception as e:
            print(f"Lỗi khi lọc lịch sử giá: {str(e)}")
            QMessageBox.warning(self, "Lỗi lọc dữ liệu", f"Đã xảy ra lỗi khi lọc dữ liệu: {str(e)}")

    def apply_date_filter(self):
        """Áp dụng lọc theo khoảng thời gian cho lịch sử giá"""
        try:
            for row in range(self.price_table.rowCount()):
                date_item = self.price_table.item(row, 2)  # Cột ngày (index 2)
                if not date_item:
                    continue

                try:
                    # Chuyển đổi chuỗi ngày (dd.mm.yyyy) thành đối tượng QDate
                    date_str = date_item.text()
                    if date_str and "." in date_str:
                        parts = date_str.split(".")
                        if len(parts) == 3:
                            day, month, year = int(parts[0]), int(parts[1]), int(parts[2])
                            item_date = QDate(year, month, day)

                            # So sánh với khoảng thời gian đã chọn
                            if (item_date < self.date_from.date() or
                                item_date > self.date_to.date()):
                                self.price_table.setRowHidden(row, True)
                            else:
                                self.price_table.setRowHidden(row, False)
                        else:
                            # Không phải định dạng ngày tháng hợp lệ, hiển thị mặc định
                            self.price_table.setRowHidden(row, False)
                    else:
                        # Không có ngày, hiển thị mặc định
                        self.price_table.setRowHidden(row, False)
                except Exception as e:
                    print(f"Lỗi khi lọc dòng {row}: {str(e)}")
                    self.price_table.setRowHidden(row, False)
        except Exception as e:
            print(f"Lỗi khi áp dụng bộ lọc ngày: {str(e)}")
            QMessageBox.warning(self, "Lỗi lọc dữ liệu", f"Đã xảy ra lỗi khi lọc dữ liệu: {str(e)}")

    def create_price_history_tab(self):
        """Tạo tab hiển thị thông tin lịch sử giá"""
        price_tab = QWidget()
        layout = QVBoxLayout(price_tab)

        # Search bar
        search_layout = QHBoxLayout()
        search_label = QLabel("Tìm kiếm:")
        self.price_search = QLineEdit()
        self.price_search.setPlaceholderText("Nhập tên sản phẩm để tìm kiếm lịch sử giá...")
        self.price_search.textChanged.connect(self.filter_prices)

        # Thêm lọc theo khoảng thời gian
        date_label = QLabel("Khoảng thời gian:")
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addMonths(-3))
        self.date_from.setCalendarPopup(True)
        self.date_from.setDisplayFormat("dd.MM.yyyy")

        to_label = QLabel("đến")
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        self.date_to.setCalendarPopup(True)
        self.date_to.setDisplayFormat("dd.MM.yyyy")

        apply_filter_btn = QPushButton("Áp dụng")
        apply_filter_btn.clicked.connect(self.apply_date_filter)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.price_search)
        search_layout.addStretch()
        search_layout.addWidget(date_label)
        search_layout.addWidget(self.date_from)
        search_layout.addWidget(to_label)
        search_layout.addWidget(self.date_to)
        search_layout.addWidget(apply_filter_btn)
        layout.addLayout(search_layout)

        # Bảng lịch sử giá
        self.price_table = QTableWidget()
        self.price_table.setColumnCount(4)  # Giảm số cột từ 5 xuống 4 (đã xóa cột "Nguồn")
        self.price_table.setHorizontalHeaderLabels([
            "Tên sản phẩm", "Giá", "Phiên Live", "Thời gian lưu"
        ])
        self.price_table.horizontalHeader().setStretchLastSection(True)
        self.price_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.price_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.price_table.setSortingEnabled(True)
        self.price_table.setAlternatingRowColors(True)

        # Thiết lập độ rộng cột
        self.price_table.setColumnWidth(0, 350)  # Tăng độ rộng cột "Tên sản phẩm"
        self.price_table.setColumnWidth(1, 120)  # Giá
        self.price_table.setColumnWidth(2, 100)  # Phiên Live

        # Kết nối sự kiện double-click
        self.price_table.doubleClicked.connect(self.show_price_product_on_double_click)

        layout.addWidget(self.price_table)

        # Nút xem biểu đồ
        btn_layout = QHBoxLayout()
        view_chart_btn = QPushButton("Xem biểu đồ giá")
        view_chart_btn.clicked.connect(self.show_selected_price_history)

        btn_layout.addStretch()
        btn_layout.addWidget(view_chart_btn)
        layout.addLayout(btn_layout)

        self.tab_widget.addTab(price_tab, "Lịch sử giá")

    def show_price_product_on_double_click(self, index):
        """Hiển thị biểu đồ giá khi double-click vào bảng lịch sử giá"""
        try:
            row = index.row()
            product_name = self.price_table.item(row, 0).text()
            dialog = PriceHistoryDialog(product_name, self)
            dialog.exec()
        except Exception as e:
            import traceback
            error_trace = traceback.format_exc()
            self.result_add_log(f"Lỗi khi hiển thị biểu đồ giá từ double-click: {str(e)}")
            print(f"Lỗi chi tiết khi hiển thị biểu đồ giá:\n{error_trace}")
            QMessageBox.warning(self, "Lỗi", f"Không thể hiển thị biểu đồ giá: {str(e)}")

    def show_product_price_history(self, index):
        """Hiển thị lịch sử giá của sản phẩm được chọn thông qua double click"""
        # Lấy dòng từ chỉ mục
        row = index.row()
        # Lấy tên sản phẩm từ cột đầu tiên
        product_name = self.product_table.item(row, 0).text()

        # Tạo và hiển thị dialog lịch sử giá
        try:
            dialog = PriceHistoryDialog(product_name, self)
            dialog.exec()
        except Exception as e:
            import traceback
            error_trace = traceback.format_exc()
            self.result_add_log(f"Lỗi khi hiển thị lịch sử giá: {str(e)}")
            print(f"Lỗi chi tiết khi hiển thị lịch sử giá:\n{error_trace}")
            QMessageBox.warning(self, "Lỗi", f"Không thể hiển thị lịch sử giá: {str(e)}")

    def show_selected_price_history(self):
        """Hiển thị biểu đồ giá cho sản phẩm được chọn trong bảng lịch sử giá"""
        selected_rows = self.price_table.selectionModel().selectedRows()
        if not selected_rows:
            QMessageBox.information(self, "Thông báo", "Vui lòng chọn một sản phẩm để xem biểu đồ giá")
            return

        row = selected_rows[0].row()
        product_name = self.price_table.item(row, 0).text()

        dialog = PriceHistoryDialog(product_name, self)
        dialog.exec()

    def refresh_data(self):
        """Làm mới tất cả dữ liệu trong các tab"""
        try:
            self.result_add_log("Bắt đầu làm mới dữ liệu...")

            # Kiểm tra và đảm bảo database tồn tại
            if not os.path.exists(str(DB_PATH)):
                self.result_add_log("Database không tồn tại. Đang tạo database mới...")
                setup_database()
                check_and_update_table_structure()

            # Tải lại dữ liệu cho tất cả các tab
            # Bắt đầu với tab hiện tại để hiển thị ngay thay đổi
            current_index = self.tab_widget.currentIndex()

            # Tải lại dữ liệu cho tab hiện tại trước
            if current_index == 0:  # Tab sản phẩm
                self.load_product_data()
            elif current_index == 1:  # Tab lịch sử giá
                self.load_price_data()
            elif current_index == 2:  # Tab thương hiệu
                self.load_brand_data()

            # Sau đó tải dữ liệu cho các tab khác
            if current_index != 0:
                self.load_product_data()
            if current_index != 1:
                self.load_price_data()
            if current_index != 2:
                self.load_brand_data()

            self.result_add_log("Làm mới dữ liệu hoàn tất!")
            QMessageBox.information(self, "Thông báo", "Đã làm mới dữ liệu thành công!")
        except Exception as e:
            self.result_add_log(f"Lỗi khi làm mới dữ liệu: {str(e)}")
            show_user_friendly_error(
                self,
                str(e),
                "Không thể làm mới dữ liệu. Vui lòng kiểm tra kết nối đến cơ sở dữ liệu."
            )

    def start_auto_price_update(self):
        """Bắt đầu quá trình cập nhật giá tự động từ Deal list"""
        parent_window = self.parent_widget

        # Kiểm tra xem có spreadsheet đã load không
        if not hasattr(parent_window, 'spreadsheet') or not parent_window.spreadsheet:
            # Hiển thị thông báo yêu cầu tải Google Sheet
            QMessageBox.warning(self, "Lỗi", "Vui lòng tải Google Sheet trước khi sử dụng tính năng này.\n\n1. Đóng dialog này\n2. Nhập link Google Sheet ở giao diện chính\n3. Nhấn 'Load Sheet'\n4. Sau đó thử lại tính năng này")
            return

    def create_brand_tab(self):
        """Tạo tab hiển thị thông tin thương hiệu"""
        brand_tab = QWidget()
        layout = QVBoxLayout(brand_tab)

        # Tạo bộ lọc
        filter_layout = QHBoxLayout()
        filter_label = QLabel("Lọc:")
        self.brand_filter = QLineEdit()
        self.brand_filter.setPlaceholderText("Nhập từ khóa để lọc...")
        self.brand_filter.textChanged.connect(self.filter_brands)
        filter_layout.addWidget(filter_label)
        filter_layout.addWidget(self.brand_filter)
        layout.addLayout(filter_layout)

        # Tạo bảng dữ liệu
        self.brand_table = QTableWidget()
        self.brand_table.setColumnCount(3)
        self.brand_table.setHorizontalHeaderLabels([
            "Mã thương hiệu", "Tên thương hiệu", "Phân loại"
        ])
        self.brand_table.horizontalHeader().setStretchLastSection(True)
        self.brand_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.brand_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.brand_table.setSortingEnabled(True)
        self.brand_table.setAlternatingRowColors(True)

        # Thiết lập độ rộng cột
        self.brand_table.setColumnWidth(0, 120)  # Mã thương hiệu
        self.brand_table.setColumnWidth(1, 200)  # Tên thương hiệu

        layout.addWidget(self.brand_table)

        self.tab_widget.addTab(brand_tab, "Thương hiệu")

    def filter_brands(self):
        """Lọc danh sách thương hiệu dựa trên từ khóa tìm kiếm"""
        try:
            search_text = self.brand_filter.text().lower()
            for row in range(self.brand_table.rowCount()):
                match = False
                for col in range(self.brand_table.columnCount()):
                    item = self.brand_table.item(row, col)
                    if item and search_text in item.text().lower():
                        match = True
                        break
                self.brand_table.setRowHidden(row, not match)
        except Exception as e:
            print(f"Lỗi khi lọc thương hiệu: {str(e)}")
            QMessageBox.warning(self, "Lỗi lọc dữ liệu", f"Đã xảy ra lỗi khi lọc dữ liệu: {str(e)}")

    def is_table_available(self, table_name):
        """Kiểm tra xem bảng có tồn tại trong cơ sở dữ liệu không"""
        try:
            conn = sqlite3.connect(str(DB_PATH))
            cursor = conn.cursor()

            # Kiểm tra bảng có tồn tại không
            if table_name == "brand_table":
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='brands'")
            elif table_name == "product_classifications":
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='product_classifications'")
            else:
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")

            result = cursor.fetchone()
            conn.close()
            return result is not None
        except Exception as e:
            print(f"Lỗi khi kiểm tra bảng {table_name}: {str(e)}")
            return False

    def load_brand_data(self):
        """Tải dữ liệu thương hiệu từ database"""
        try:
            if not self.is_table_available("brands"):
                self.result_add_log("Bảng thương hiệu không có sẵn.")
                return

            conn = sqlite3.connect(str(DB_PATH))
            cursor = conn.cursor()

            # Lấy dữ liệu từ bảng brands
            cursor.execute("SELECT brand_code, brand_name, classification FROM brands ORDER BY brand_code")
            rows = cursor.fetchall()

            # Xóa dữ liệu cũ
            self.brand_table.setRowCount(0)

            # Thêm dữ liệu mới
            self.brand_table.setRowCount(len(rows))
            for i, (brand_code, brand_name, classification) in enumerate(rows):
                self.brand_table.setItem(i, 0, QTableWidgetItem(brand_code or ""))
                self.brand_table.setItem(i, 1, QTableWidgetItem(brand_name or ""))
                self.brand_table.setItem(i, 2, QTableWidgetItem(classification or ""))

            conn.close()
            self.result_add_log(f"Đã tải {len(rows)} thương hiệu từ database")

        except Exception as e:
            self.result_add_log(f"Lỗi khi tải dữ liệu thương hiệu: {str(e)}")
            print(f"Chi tiết lỗi khi tải dữ liệu thương hiệu: {str(e)}")

    def load_product_data(self):
        """Tải dữ liệu sản phẩm từ database"""
        try:
            if not self.is_table_available("product_classifications"):
                self.result_add_log("Bảng sản phẩm không có sẵn.")
                return

            conn = sqlite3.connect(str(DB_PATH))
            cursor = conn.cursor()

            # Kiểm tra xem bảng product_details có tồn tại không
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='product_details'")
            has_product_details = cursor.fetchone() is not None

            # Lấy dữ liệu từ bảng product_classifications
            # Kết hợp với thông tin giá mới nhất từ price_history
            if has_product_details:
                # Nếu có bảng product_details, sử dụng JOIN
                query = """
                SELECT
                    p.product_name,
                    p.brand_code,
                    p.classification,
                    COALESCE(d.product_name_only, '') as product_name_only,
                    COALESCE(d.code, '') as code,
                    COALESCE(d.capacity, '') as capacity,
                    COALESCE(d.quantity, 1) as quantity,
                    COALESCE((SELECT price FROM price_history WHERE product_name = p.product_name ORDER BY timestamp DESC LIMIT 1), 0) as latest_price
                FROM
                    product_classifications p
                LEFT JOIN
                    product_details d ON p.product_name = d.product_name
                ORDER BY p.product_name
                """
            else:
                # Nếu không có bảng product_details, chỉ lấy dữ liệu từ product_classifications
                query = """
                SELECT
                    p.product_name,
                    p.brand_code,
                    p.classification,
                    '' as product_name_only,
                    '' as code,
                    '' as capacity,
                    1 as quantity,
                    COALESCE((SELECT price FROM price_history WHERE product_name = p.product_name ORDER BY timestamp DESC LIMIT 1), 0) as latest_price
                FROM
                    product_classifications p
                ORDER BY p.product_name
                """

            cursor.execute(query)
            rows = cursor.fetchall()

            # Xóa dữ liệu cũ
            self.product_table.setRowCount(0)

            # Thêm dữ liệu mới
            self.product_table.setRowCount(len(rows))
            for i, (product_name, brand_code, classification, product_name_only,
                    code, capacity, quantity, latest_price) in enumerate(rows):

                # Format giá tiền
                price_str = f"{int(latest_price):,}₫" if latest_price else ""

                self.product_table.setItem(i, 0, QTableWidgetItem(product_name or ""))
                self.product_table.setItem(i, 1, QTableWidgetItem(brand_code or ""))
                self.product_table.setItem(i, 2, QTableWidgetItem(classification or ""))
                self.product_table.setItem(i, 3, QTableWidgetItem(product_name_only or ""))
                self.product_table.setItem(i, 4, QTableWidgetItem(code or ""))
                self.product_table.setItem(i, 5, QTableWidgetItem(capacity or ""))
                self.product_table.setItem(i, 6, QTableWidgetItem(str(quantity) if quantity else "1"))
                self.product_table.setItem(i, 7, QTableWidgetItem(price_str))

            conn.close()
            self.result_add_log(f"Đã tải {len(rows)} sản phẩm từ database")

        except Exception as e:
            self.result_add_log(f"Lỗi khi tải dữ liệu sản phẩm: {str(e)}")
            print(f"Chi tiết lỗi khi tải dữ liệu sản phẩm: {str(e)}")

    def load_price_data(self):
        """Tải dữ liệu lịch sử giá từ database"""
        try:
            # Kết nối tới database
            conn = sqlite3.connect(str(DB_PATH))
            cursor = conn.cursor()

            # Truy vấn dữ liệu lịch sử giá, không lấy cột source
            cursor.execute("""
                SELECT product_name, price, live_session, timestamp
                FROM price_history
                ORDER BY timestamp DESC
            """)

            # Lấy tất cả dữ liệu
            price_data = cursor.fetchall()

            # Đóng kết nối
            conn.close()

            # Xóa tất cả dữ liệu hiện tại trên bảng
            self.price_table.setRowCount(0)

            # Thiết lập số dòng cho bảng
            self.price_table.setRowCount(len(price_data))

            # Điền dữ liệu vào bảng
            for i, row in enumerate(price_data):
                # Tên sản phẩm
                product_name_item = QTableWidgetItem(row[0])
                self.price_table.setItem(i, 0, product_name_item)

                # Giá
                price_item = QTableWidgetItem(f"{row[1]:,}đ".replace(',', '.'))
                self.price_table.setItem(i, 1, price_item)

                # Live session
                live_session_item = QTableWidgetItem(row[2])
                self.price_table.setItem(i, 2, live_session_item)

                # Timestamp - Hiển thị theo múi giờ GMT+7
                try:
                    ts = datetime.strptime(row[3], "%Y-%m-%d %H:%M:%S")
                    # Chuyển đổi sang múi giờ GMT+7 (thêm 7 giờ)
                    ts = ts.replace(hour=ts.hour + 7)
                    formatted_ts = ts.strftime("%d/%m/%Y %H:%M:%S")
                except Exception as e:
                    print(f"Lỗi khi định dạng timestamp: {e}")
                    formatted_ts = row[3]

                timestamp_item = QTableWidgetItem(formatted_ts)
                self.price_table.setItem(i, 3, timestamp_item)

            # Ghi log số mục đã tải
            try:
                logger.info(f"Da tai {len(price_data)} muc tu price_history")
            except UnicodeEncodeError:
                # Tránh lỗi mã hóa ký tự Unicode
                print(f"Da tai {len(price_data)} muc tu price_history")

        except Exception as e:
            logger.error(f"Lỗi khi tải dữ liệu lịch sử giá: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

# Function to save similarity threshold
def save_similarity_threshold(value):
    """Save the similarity threshold to settings"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        cursor.execute(
            "INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)",
            ("similarity_threshold", str(value))
        )
        conn.commit()
        conn.close()
    except Exception as e:
        print(f"Error saving similarity threshold: {e}")

# Function to get similarity threshold
def get_similarity_threshold():
    """Get the saved similarity threshold from settings"""
    try:
        conn = sqlite3.connect(str(DB_PATH))
        cursor = conn.cursor()
        cursor.execute("SELECT value FROM settings WHERE key = ?", ("similarity_threshold",))
        result = cursor.fetchone()
        conn.close()

        if result:
            return float(result[0])
        return 0.95  # Default value if not found
    except Exception as e:
        print(f"Error getting similarity threshold: {e}")
        return 0.95  # Default value if error

# Thêm các phương thức filter_products và filter_prices vào lớp DatabaseManagerDialog
