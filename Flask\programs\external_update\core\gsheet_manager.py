"""
Google Sheets Manager cho External Update
Quản lý kết nối và thao tác với Google Sheets API
"""

import os
import json
import base64
import logging
from typing import Optional, Dict, Any, List
import gspread
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
from google_auth_oauthlib.flow import Flow
import tempfile

class GoogleSheetManager:
    """Quản lý kết nối Google Sheets cho External Update"""
    
    def __init__(self, credentials_base64: str = None):
        """
        Khởi tạo GoogleSheetManager
        
        Args:
            credentials_base64: Chuỗi base64 chứa thông tin OAuth client
        """
        self.credentials_base64 = credentials_base64
        self.gc = None
        self.credentials = None
        self.token_file = os.path.join(
            os.environ.get('LOCALAPPDATA', os.path.expanduser('~')), 
            'Data All in One', 
            'External Update',
            'token.json'
        )
        
        # <PERSON><PERSON><PERSON> <PERSON><PERSON> mục lưu token nếu chưa có
        os.makedirs(os.path.dirname(self.token_file), exist_ok=True)
        
        # Thiết lập logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def _decode_credentials(self) -> Dict[str, Any]:
        """Giải mã credentials từ base64"""
        try:
            if not self.credentials_base64:
                raise ValueError("Credentials base64 không được cung cấp")
            
            decoded_bytes = base64.b64decode(self.credentials_base64)
            credentials_info = json.loads(decoded_bytes.decode('utf-8'))
            return credentials_info
        except Exception as e:
            self.logger.error(f"Lỗi khi giải mã credentials: {str(e)}")
            raise
    
    def _load_token(self) -> Optional[Credentials]:
        """Tải token từ file"""
        try:
            if os.path.exists(self.token_file):
                with open(self.token_file, 'r') as f:
                    token_data = json.load(f)
                
                credentials = Credentials(
                    token=token_data.get('token'),
                    refresh_token=token_data.get('refresh_token'),
                    token_uri=token_data.get('token_uri'),
                    client_id=token_data.get('client_id'),
                    client_secret=token_data.get('client_secret'),
                    scopes=token_data.get('scopes')
                )
                
                # Refresh token nếu cần
                if credentials.expired and credentials.refresh_token:
                    credentials.refresh(Request())
                    self._save_token(credentials)
                
                return credentials
        except Exception as e:
            self.logger.warning(f"Không thể tải token: {str(e)}")
        
        return None
    
    def _save_token(self, credentials: Credentials):
        """Lưu token vào file"""
        try:
            token_data = {
                'token': credentials.token,
                'refresh_token': credentials.refresh_token,
                'token_uri': credentials.token_uri,
                'client_id': credentials.client_id,
                'client_secret': credentials.client_secret,
                'scopes': credentials.scopes
            }
            
            with open(self.token_file, 'w') as f:
                json.dump(token_data, f, indent=2)
            
            self.logger.info("Token đã được lưu thành công")
        except Exception as e:
            self.logger.error(f"Lỗi khi lưu token: {str(e)}")
    
    def authenticate(self) -> bool:
        """
        Xác thực với Google Sheets API
        
        Returns:
            bool: True nếu xác thực thành công
        """
        try:
            # Thử tải token hiện có
            self.credentials = self._load_token()
            
            if self.credentials and self.credentials.valid:
                self.gc = gspread.authorize(self.credentials)
                self.logger.info("Xác thực thành công với token hiện có")
                return True
            
            # Nếu không có token hợp lệ, thực hiện OAuth flow
            return self._perform_oauth_flow()
            
        except Exception as e:
            self.logger.error(f"Lỗi xác thực: {str(e)}")
            return False
    
    def _perform_oauth_flow(self) -> bool:
        """Thực hiện OAuth flow để lấy token mới"""
        try:
            credentials_info = self._decode_credentials()
            
            # Tạo file credentials tạm thời
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(credentials_info, f)
                temp_creds_file = f.name
            
            try:
                # Thiết lập OAuth flow
                scopes = [
                    'https://www.googleapis.com/auth/spreadsheets',
                    'https://www.googleapis.com/auth/drive'
                ]
                
                flow = Flow.from_client_secrets_file(
                    temp_creds_file,
                    scopes=scopes,
                    redirect_uri='http://localhost'
                )
                
                # Lấy authorization URL
                auth_url, _ = flow.authorization_url(prompt='consent')
                
                self.logger.info("Vui lòng truy cập URL sau để xác thực:")
                self.logger.info(auth_url)
                
                # Nhận authorization code từ user
                auth_code = input("Nhập authorization code: ").strip()
                
                # Lấy token
                flow.fetch_token(code=auth_code)
                self.credentials = flow.credentials
                
                # Lưu token
                self._save_token(self.credentials)
                
                # Tạo gspread client
                self.gc = gspread.authorize(self.credentials)
                
                self.logger.info("Xác thực OAuth thành công")
                return True
                
            finally:
                # Xóa file credentials tạm thời
                if os.path.exists(temp_creds_file):
                    os.unlink(temp_creds_file)
                    
        except Exception as e:
            self.logger.error(f"Lỗi OAuth flow: {str(e)}")
            return False
    
    def open_by_key(self, spreadsheet_id: str):
        """
        Mở spreadsheet bằng ID
        
        Args:
            spreadsheet_id: ID của spreadsheet
            
        Returns:
            Spreadsheet object
        """
        if not self.gc:
            raise RuntimeError("Chưa xác thực với Google Sheets API")
        
        try:
            return self.gc.open_by_key(spreadsheet_id)
        except Exception as e:
            self.logger.error(f"Lỗi khi mở spreadsheet {spreadsheet_id}: {str(e)}")
            raise
    
    def open_by_url(self, url: str):
        """
        Mở spreadsheet bằng URL
        
        Args:
            url: URL của spreadsheet
            
        Returns:
            Spreadsheet object
        """
        if not self.gc:
            raise RuntimeError("Chưa xác thực với Google Sheets API")
        
        try:
            return self.gc.open_by_url(url)
        except Exception as e:
            self.logger.error(f"Lỗi khi mở spreadsheet từ URL: {str(e)}")
            raise
    
    def get_sheets_service(self):
        """
        Lấy Google Sheets service object
        
        Returns:
            Google Sheets service object
        """
        if not self.credentials:
            raise RuntimeError("Chưa xác thực với Google Sheets API")
        
        try:
            from googleapiclient.discovery import build
            service = build('sheets', 'v4', credentials=self.credentials)
            return service
        except Exception as e:
            self.logger.error(f"Lỗi khi tạo Sheets service: {str(e)}")
            raise
    
    def test_connection(self) -> bool:
        """
        Test kết nối với Google Sheets API
        
        Returns:
            bool: True nếu kết nối thành công
        """
        try:
            if not self.gc:
                return False
            
            # Thử lấy danh sách spreadsheets (chỉ để test)
            # Không thực sự lấy vì có thể không có quyền
            return True
            
        except Exception as e:
            self.logger.error(f"Test kết nối thất bại: {str(e)}")
            return False
    
    def is_authenticated(self) -> bool:
        """
        Kiểm tra xem đã xác thực chưa
        
        Returns:
            bool: True nếu đã xác thực
        """
        return self.gc is not None and self.credentials is not None
    
    def get_credentials_info(self) -> Dict[str, Any]:
        """
        Lấy thông tin credentials hiện tại
        
        Returns:
            Dict chứa thông tin credentials
        """
        if not self.credentials:
            return {}
        
        return {
            'client_id': getattr(self.credentials, 'client_id', None),
            'scopes': getattr(self.credentials, 'scopes', []),
            'expired': getattr(self.credentials, 'expired', True),
            'valid': getattr(self.credentials, 'valid', False)
        }
