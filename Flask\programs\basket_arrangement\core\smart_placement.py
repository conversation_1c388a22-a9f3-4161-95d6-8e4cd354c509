"""
Smart Placement Engine - Copy đầy đủ từ basket_package.py
Đả<PERSON> bảo 100% tính toàn vẹn logic xử lý placement thông minh
"""

import random
from .constants import (
    MIN_DISTANCE_BETWEEN_INPUT_IDS,
    PLACEMENT_PRIORITY_WEIGHTS,
    MESSAGE_TEMPLATES
)

class SmartPlacementEngine:
    """
    Engine xử lý placement thông minh cho ID input.
    Tôn trọng shop grouping và phân tán ngẫu nhiên một cách thông minh.
    """

    def __init__(self, deal_list_manager, log_function=None):
        self.deal_list_manager = deal_list_manager
        self.log = log_function or print

    def analyze_current_structure(self, current_ids):
        """
        Phân tích cấu trúc hiện tại của danh sách ID để xác định:
        - Vị trí trống
        - Nhóm shop liên tục
        - Vị trí boundary giữa các shop
        """
        structure = {
            'empty_positions': [],
            'shop_groups': {},  # {shop_id: [positions]}
            'boundary_positions': [],
            'group_edge_positions': [],
            'group_middle_positions': []
        }

        # Phân tích từng vị trí
        for pos, id_val in enumerate(current_ids):
            if not id_val or id_val.strip() == "":
                structure['empty_positions'].append(pos)
            else:
                shop_id = self.deal_list_manager.id_to_shop.get(id_val, "unknown")
                if shop_id not in structure['shop_groups']:
                    structure['shop_groups'][shop_id] = []
                structure['shop_groups'][shop_id].append(pos)

        # Tìm các nhóm shop liên tục
        continuous_groups = {}
        for shop_id, positions in structure['shop_groups'].items():
            if len(positions) <= 1:
                continue

            positions.sort()
            continuous_groups[shop_id] = []
            current_group = [positions[0]]

            for i in range(1, len(positions)):
                if positions[i] == positions[i-1] + 1:
                    current_group.append(positions[i])
                else:
                    if len(current_group) > 1:
                        continuous_groups[shop_id].append(current_group)
                    current_group = [positions[i]]

            if len(current_group) > 1:
                continuous_groups[shop_id].append(current_group)

        # Xác định boundary positions (giữa 2 shop khác nhau)
        for pos in range(len(current_ids) - 1):
            current_id = current_ids[pos]
            next_id = current_ids[pos + 1]

            if current_id and next_id:
                current_shop = self.deal_list_manager.id_to_shop.get(current_id, "")
                next_shop = self.deal_list_manager.id_to_shop.get(next_id, "")

                if current_shop != next_shop and current_shop and next_shop:
                    structure['boundary_positions'].append(pos + 1)

        # Xác định group edge và middle positions
        for shop_id, groups in continuous_groups.items():
            for group in groups:
                if len(group) >= 2:
                    # Đầu và cuối nhóm
                    structure['group_edge_positions'].extend([group[0], group[-1]])
                    # Giữa nhóm (nếu có >= 3 phần tử)
                    if len(group) >= 3:
                        structure['group_middle_positions'].extend(group[1:-1])

        return structure, continuous_groups

    def find_optimal_positions(self, current_ids, input_ids, top_limits, is_grouped_flags):
        """
        Tìm vị trí tối ưu cho các ID input dựa trên thuật toán smart placement.

        Args:
            current_ids: Danh sách ID hiện tại
            input_ids: Danh sách ID input cần chèn
            top_limits: Dict {id: top_limit}
            is_grouped_flags: Dict {id: is_grouped}

        Returns:
            List[(id, position, placement_type)]
        """
        self.log(MESSAGE_TEMPLATES['SMART_PLACEMENT_START'].format(count=len(input_ids)))

        # Phân tích cấu trúc hiện tại
        structure, continuous_groups = self.analyze_current_structure(current_ids)

        self.log(MESSAGE_TEMPLATES['STRUCTURE_ANALYSIS'])
        self.log(MESSAGE_TEMPLATES['EMPTY_POSITIONS'].format(count=len(structure['empty_positions'])))
        self.log(MESSAGE_TEMPLATES['BOUNDARY_POSITIONS'].format(count=len(structure['boundary_positions'])))
        self.log(MESSAGE_TEMPLATES['GROUP_EDGE_POSITIONS'].format(count=len(structure['group_edge_positions'])))
        self.log(MESSAGE_TEMPLATES['GROUP_MIDDLE_POSITIONS'].format(count=len(structure['group_middle_positions'])))

        # Phân loại ID input theo nhóm
        grouped_ids = [id_val for id_val in input_ids if is_grouped_flags.get(id_val, False)]
        non_grouped_ids = [id_val for id_val in input_ids if not is_grouped_flags.get(id_val, False)]

        self.log(MESSAGE_TEMPLATES['ID_CLASSIFICATION'])
        self.log(MESSAGE_TEMPLATES['GROUPED_IDS'].format(count=len(grouped_ids)))
        self.log(MESSAGE_TEMPLATES['NON_GROUPED_IDS'].format(count=len(non_grouped_ids)))

        placement_results = []

        # Xử lý ID nhóm trước (sử dụng thuật toán cũ)
        if grouped_ids:
            self.log(MESSAGE_TEMPLATES['PROCESSING_GROUPED'].format(count=len(grouped_ids)))
            for id_val in grouped_ids:
                top_limit = top_limits.get(id_val, len(current_ids))
                max_pos = min(top_limit - 1, len(current_ids))
                position = random.randint(0, max_pos)
                placement_results.append((id_val, position, 'GROUPED'))

        # Xử lý ID không nhóm với smart placement
        if non_grouped_ids:
            self.log(MESSAGE_TEMPLATES['PROCESSING_NON_GROUPED'].format(count=len(non_grouped_ids)))
            smart_results = self._smart_place_non_grouped_ids(
                current_ids, non_grouped_ids, top_limits, structure
            )
            placement_results.extend(smart_results)

        self.log(MESSAGE_TEMPLATES['SMART_PLACEMENT_END'])
        return placement_results

    def _smart_place_non_grouped_ids(self, current_ids, input_ids, top_limits, structure):
        """Xử lý placement cho ID không nhóm với thuật toán thông minh"""

        # Nhóm ID theo top_limit để xử lý bottleneck
        top_limit_groups = {}
        for id_val in input_ids:
            top_limit = top_limits.get(id_val, len(current_ids))
            if top_limit not in top_limit_groups:
                top_limit_groups[top_limit] = []
            top_limit_groups[top_limit].append(id_val)

        placement_results = []

        for top_limit, ids_in_group in top_limit_groups.items():
            self.log(f"\nXử lý nhóm {len(ids_in_group)} ID với top limit {top_limit}")

            # Tạo pool vị trí theo mức độ ưu tiên
            priority_pools = self._create_priority_pools(structure, top_limit, len(current_ids))

            # Phân bổ vị trí cho nhóm ID này
            group_placements = self._distribute_ids_to_pools(
                ids_in_group, priority_pools, top_limit
            )

            placement_results.extend(group_placements)

        return placement_results

    def _create_priority_pools(self, structure, top_limit, current_length):
        """Tạo các pool vị trí theo mức độ ưu tiên, loại trừ avoid_positions"""
        # Tính avoid_positions để loại trừ các vị trí bị cấm
        avoid_positions_limit = self._get_avoid_positions_for_top(top_limit)

        # QUAN TRỌNG: max_pos phải là top_limit - 1, không phải current_length
        max_pos = top_limit - 1

        pools = {
            'EMPTY_SLOT': [],
            'BOUNDARY': [],
            'GROUP_EDGE': [],
            'GROUP_MIDDLE': []
        }

        # Pool A: Vị trí trống trong phạm vi top_limit, loại trừ avoid_positions
        pools['EMPTY_SLOT'] = [pos for pos in structure['empty_positions']
                              if avoid_positions_limit <= pos <= max_pos]

        # Pool B: Vị trí boundary trong phạm vi top_limit, loại trừ avoid_positions
        pools['BOUNDARY'] = [pos for pos in structure['boundary_positions']
                            if avoid_positions_limit <= pos <= max_pos]

        # Pool C: Vị trí group edge trong phạm vi top_limit, loại trừ avoid_positions
        pools['GROUP_EDGE'] = [pos for pos in structure['group_edge_positions']
                              if avoid_positions_limit <= pos <= max_pos]

        # Pool D: Vị trí group middle trong phạm vi top_limit, loại trừ avoid_positions
        pools['GROUP_MIDDLE'] = [pos for pos in structure['group_middle_positions']
                                if avoid_positions_limit <= pos <= max_pos]

        # Log thông tin pools
        self.log(f"  Avoid positions: 0-{avoid_positions_limit-1} (bị loại trừ)")
        self.log(f"  Phạm vi cho phép: {avoid_positions_limit}-{max_pos}")
        for pool_name, positions in pools.items():
            self.log(f"  Pool {pool_name}: {len(positions)} vị trí")

        return pools

    def _get_avoid_positions_for_top(self, top_limit):
        """Tính avoid_positions dựa trên top_limit (logic giống TimeSlotProcessor.get_avoid_positions)"""
        if top_limit == 20: return 3
        elif top_limit == 30: return 6
        elif top_limit == 50: return 10
        elif top_limit == 100: return 20
        elif top_limit == 150: return 30
        elif top_limit == 200: return 100
        elif top_limit == 250: return 120
        else: return top_limit // 5

    def _distribute_ids_to_pools(self, input_ids, priority_pools, top_limit):
        """Phân bổ ID vào các pool theo thứ tự ưu tiên"""
        placement_results = []
        remaining_ids = input_ids.copy()
        used_positions = set()  # Theo dõi các vị trí đã sử dụng

        # Thứ tự ưu tiên pools
        pool_order = ['EMPTY_SLOT', 'BOUNDARY', 'GROUP_EDGE', 'GROUP_MIDDLE']

        for pool_name in pool_order:
            if not remaining_ids:
                break

            # Lọc ra các vị trí chưa được sử dụng
            available_positions = [pos for pos in priority_pools[pool_name]
                                 if pos not in used_positions]
            if not available_positions:
                continue

            # Số lượng ID có thể đặt vào pool này
            ids_to_place = min(len(remaining_ids), len(available_positions))

            if ids_to_place > 0:
                # Random chọn vị trí KHÁC NHAU
                selected_positions = random.sample(available_positions, ids_to_place)
                selected_ids = remaining_ids[:ids_to_place]

                # Tạo assignments trực tiếp (không dùng adaptive randomness để tránh lỗi)
                final_assignments = []
                for i, id_val in enumerate(selected_ids):
                    if i < len(selected_positions):
                        position = selected_positions[i]
                        final_assignments.append((id_val, position, pool_name))
                        used_positions.add(position)  # Đánh dấu vị trí đã sử dụng
                        self.log(f"    Gán {id_val} -> vị trí {position} (pool: {pool_name})")

                placement_results.extend(final_assignments)
                remaining_ids = remaining_ids[ids_to_place:]

                self.log(f"  Đặt {len(final_assignments)} ID vào pool {pool_name}")

        # Xử lý ID còn lại (nếu có) - buộc phải chèn
        if remaining_ids:
            self.log(f"  Còn {len(remaining_ids)} ID cần xử lý bằng fallback")
            fallback_results = self._fallback_placement_with_used_positions(
                remaining_ids, top_limit, used_positions
            )
            placement_results.extend(fallback_results)

        return placement_results

    def _apply_adaptive_randomness(self, ids, positions, pool_type):
        """Áp dụng randomness thông minh để tránh tạo pattern"""
        if len(ids) <= 1:
            return [(ids[0], positions[0], pool_type)] if ids and positions else []

        # Sắp xếp vị trí để kiểm tra khoảng cách
        sorted_positions = sorted(positions)

        # Áp dụng khoảng cách tối thiểu nếu có thể
        filtered_positions = []
        last_selected = -MIN_DISTANCE_BETWEEN_INPUT_IDS - 1

        for pos in sorted_positions:
            if pos - last_selected >= MIN_DISTANCE_BETWEEN_INPUT_IDS:
                filtered_positions.append(pos)
                last_selected = pos

        # Nếu không đủ vị trí với khoảng cách tối thiểu, sử dụng tất cả
        if len(filtered_positions) < len(ids):
            filtered_positions = positions

        # Random shuffle để tránh pattern
        random.shuffle(filtered_positions)
        random.shuffle(ids)

        # Tạo kết quả
        results = []
        for i, id_val in enumerate(ids):
            if i < len(filtered_positions):
                results.append((id_val, filtered_positions[i], pool_type))

        return results

    def _fallback_placement(self, remaining_ids, top_limit):
        """Xử lý fallback cho ID không thể đặt vào pool nào"""
        results = []
        for id_val in remaining_ids:
            # Đặt ở cuối phạm vi top_limit
            position = min(top_limit - 1, top_limit - len(remaining_ids) + len(results))
            results.append((id_val, position, 'FALLBACK'))
        return results

    def _fallback_placement_with_used_positions(self, remaining_ids, top_limit, used_positions):
        """Xử lý fallback cho ID còn lại, tránh các vị trí đã sử dụng và tôn trọng avoid_positions"""
        results = []

        # Tính avoid_positions để loại trừ các vị trí bị cấm
        avoid_positions_limit = self._get_avoid_positions_for_top(top_limit)
        max_pos = top_limit - 1

        # Tìm các vị trí chưa được sử dụng trong phạm vi cho phép (từ avoid_positions_limit đến max_pos)
        available_positions = []
        for pos in range(avoid_positions_limit, max_pos + 1):
            if pos not in used_positions:
                available_positions.append(pos)

        # Nếu không đủ vị trí trong phạm vi top_limit, mở rộng ra ngoài top_limit
        while len(available_positions) < len(remaining_ids):
            max_pos += 1
            if max_pos not in used_positions:
                available_positions.append(max_pos)

        # Random chọn vị trí cho các ID còn lại
        if len(available_positions) >= len(remaining_ids):
            selected_positions = random.sample(available_positions, len(remaining_ids))
        else:
            # Trường hợp khẩn cấp: không đủ vị trí
            selected_positions = available_positions + list(range(max_pos + 1, max_pos + 1 + len(remaining_ids) - len(available_positions)))

        for i, id_val in enumerate(remaining_ids):
            position = selected_positions[i]
            results.append((id_val, position, 'FALLBACK'))
            self.log(f"    Fallback: {id_val} -> vị trí {position} (avoid_limit: {avoid_positions_limit})")

        return results

    def find_optimal_insertion_for_review_id(self, current_ids, input_id, target_position, top_limit):
        """
        Tìm vị trí chèn tối ưu cho ID input khi vị trí target có ID review cùng shop.
        Tôn trọng shop grouping bằng cách tìm vị trí không phá vỡ nhóm shop.

        Args:
            current_ids: Danh sách ID hiện tại
            input_id: ID input cần chèn
            target_position: Vị trí ban đầu muốn chèn
            top_limit: Giới hạn top

        Returns:
            int: Vị trí tối ưu để chèn
        """
        if target_position >= len(current_ids):
            return target_position

        target_id = current_ids[target_position]
        if not target_id or not self.deal_list_manager.has_review(target_id):
            return target_position  # Không phải ID review, chèn bình thường

        target_shop = self.deal_list_manager.id_to_shop.get(target_id, "")
        if not target_shop:
            return target_position

        self.log(f"Phát hiện xung đột với ID review {target_id} (shop: {target_shop}) tại vị trí {target_position}")

        # Tìm ranh giới nhóm shop hiện tại
        group_start, group_end = self._find_shop_group_boundaries(current_ids, target_position, target_shop)

        self.log(f"Nhóm shop {target_shop} từ vị trí {group_start} đến {group_end}")

        # Tìm vị trí chèn tối ưu
        max_search_pos = min(top_limit - 1, len(current_ids) - 1)
        avoid_positions_limit = self._get_avoid_positions_for_top(top_limit)

        # Ưu tiên 1: Vị trí trước nhóm shop (phải >= avoid_positions_limit)
        if group_start > 0:
            candidate_pos = group_start
            if candidate_pos <= max_search_pos and candidate_pos >= avoid_positions_limit:
                prev_id = current_ids[candidate_pos - 1] if candidate_pos > 0 else None
                if not prev_id or self.deal_list_manager.id_to_shop.get(prev_id, "") != target_shop:
                    self.log(f"Tìm thấy vị trí tối ưu trước nhóm: {candidate_pos}")
                    return candidate_pos

        # Ưu tiên 2: Vị trí sau nhóm shop (phải >= avoid_positions_limit)
        if group_end < len(current_ids) - 1:
            candidate_pos = group_end + 1
            if candidate_pos <= max_search_pos and candidate_pos >= avoid_positions_limit:
                next_id = current_ids[candidate_pos] if candidate_pos < len(current_ids) else None
                if not next_id or self.deal_list_manager.id_to_shop.get(next_id, "") != target_shop:
                    self.log(f"Tìm thấy vị trí tối ưu sau nhóm: {candidate_pos}")
                    return candidate_pos

        # Ưu tiên 3: Tìm vị trí boundary gần nhất trong phạm vi top_limit (phải >= avoid_positions_limit)
        for distance in range(1, max_search_pos + 1):
            # Tìm về phía trước
            check_pos = target_position - distance
            if (check_pos >= avoid_positions_limit and
                check_pos >= 0 and
                self._is_boundary_position(current_ids, check_pos)):
                self.log(f"Tìm thấy boundary position phía trước: {check_pos}")
                return check_pos

            # Tìm về phía sau
            check_pos = target_position + distance
            if (check_pos >= avoid_positions_limit and
                check_pos <= max_search_pos and
                check_pos < len(current_ids) and
                self._is_boundary_position(current_ids, check_pos)):
                self.log(f"Tìm thấy boundary position phía sau: {check_pos}")
                return check_pos

        # Fallback: Chèn vào vị trí gốc (chen ngang)
        self.log(f"Không tìm được vị trí tối ưu, chen ngang vào vị trí gốc: {target_position}")
        return target_position

    def _find_shop_group_boundaries(self, current_ids, position, shop_id):
        """Tìm ranh giới của nhóm shop liên tục chứa vị trí position"""
        start = position
        end = position

        # Tìm về phía trước
        while start > 0:
            prev_id = current_ids[start - 1]
            if prev_id and self.deal_list_manager.id_to_shop.get(prev_id, "") == shop_id:
                start -= 1
            else:
                break

        # Tìm về phía sau
        while end < len(current_ids) - 1:
            next_id = current_ids[end + 1]
            if next_id and self.deal_list_manager.id_to_shop.get(next_id, "") == shop_id:
                end += 1
            else:
                break

        return start, end

    def _is_boundary_position(self, current_ids, position):
        """Kiểm tra xem vị trí có phải là boundary giữa 2 shop khác nhau không"""
        if position <= 0 or position >= len(current_ids):
            return False

        prev_id = current_ids[position - 1]
        curr_id = current_ids[position]

        if not prev_id or not curr_id:
            return True  # Có vị trí trống

        prev_shop = self.deal_list_manager.id_to_shop.get(prev_id, "")
        curr_shop = self.deal_list_manager.id_to_shop.get(curr_id, "")

        return prev_shop != curr_shop and prev_shop and curr_shop