import base64
import json
import gspread
import pickle
import os
import hashlib
from google.oauth2.service_account import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from google.auth.exceptions import RefreshError

# ------------------ CẤU HÌNH ------------------
SCOPES = [
    'https://www.googleapis.com/auth/spreadsheets',
    'https://www.googleapis.com/auth/drive'
]

# Xác định đường dẫn đến thư mục "Data All in One" trong LOCALAPPDATA
LOCAL_FOLDER = os.path.join(os.environ.get("LOCALAPPDATA", os.getcwd()), "Data All in One")
if not os.path.exists(LOCAL_FOLDER):
    os.makedirs(LOCAL_FOLDER)

# Th<PERSON> mụ<PERSON> lư<PERSON> token
TOKEN_FOLDER = os.path.join(LOCAL_FOLDER, "Tokens")
if not os.path.exists(TOKEN_FOLDER):
    os.makedirs(TOKEN_FOLDER)

def decode_base64(data):
    missing_padding = len(data) % 4
    if missing_padding:
        data += "=" * (4 - missing_padding)
    return base64.b64decode(data)

def generate_token_path(credentials_data):
    """Tạo đường dẫn đến file token dựa trên hash của credentials"""
    # Tạo hash từ credentials để đặt tên file
    hash_obj = hashlib.md5(credentials_data.encode('utf-8'))
    token_hash = hash_obj.hexdigest()[:12]
    return os.path.join(TOKEN_FOLDER, f"token_{token_hash}.pickle")

# Danh sách các OAuth2 credentials được hỗ trợ
SUPPORTED_OAUTH_CREDENTIALS = []
# File token mặc định - cho tương thích ngược
DEFAULT_TOKEN_FILE = os.path.join(LOCAL_FOLDER, "token.pickle")

class GoogleSheetManager:
    def __init__(self, auth_type='service', credentials_data=None, all_oauth_credentials=None):
        """
        auth_type: 'service' hoặc 'oauth'
        credentials_data:
            - Nếu auth_type là 'service': là chuỗi Base64 của file credentials (Service Account).
            - Nếu auth_type là 'oauth': là chuỗi Base64 của file JSON client OAuth.
        all_oauth_credentials: Danh sách các chuỗi Base64 của các file JSON client OAuth để thử (tùy chọn)
        """
        self.auth_type = auth_type
        self.client = None
        self.creds = None
        
        if auth_type == 'service':
            if credentials_data is None:
                raise ValueError("Cần cung cấp credentials_data cho Service Account")
            # Giải mã và chuyển đổi thành dict
            credentials_json = decode_base64(credentials_data).decode("utf-8")
            credentials_dict = json.loads(credentials_json)
            scope = SCOPES
            self.creds = Credentials.from_service_account_info(credentials_dict, scopes=scope)
            self.client = gspread.authorize(self.creds)
            
        elif auth_type == 'oauth':
            # Nếu có danh sách OAuth credentials để thử
            if all_oauth_credentials:
                self._try_multiple_oauth_credentials(credentials_data, all_oauth_credentials)
            else:
                # Chỉ thử với credentials_data được cung cấp
                if credentials_data is None:
                    raise ValueError("Cần cung cấp credentials_data cho OAuth")
                self._authorize_with_oauth(credentials_data)
        else:
            raise ValueError("auth_type không hợp lệ. Chỉ hỗ trợ 'service' hoặc 'oauth'.")

    def _try_multiple_oauth_credentials(self, primary_credentials, all_credentials):
        """Thử xác thực với nhiều OAuth credentials khác nhau"""
        # Thêm primary_credentials vào đầu danh sách nếu chưa có
        if primary_credentials and primary_credentials not in all_credentials:
            all_credentials = [primary_credentials] + all_credentials
        
        # Thử xác thực với từng credentials
        success = False
        last_error = None
        attempted_credentials = []
        
        for cred_data in all_credentials:
            if cred_data in attempted_credentials:
                continue  # Tránh thử lại credentials đã thử
                
            attempted_credentials.append(cred_data)
            
            try:
                self._authorize_with_oauth(cred_data)
                # Nếu xác thực thành công
                success = True
                break
            except Exception as e:
                last_error = e
                continue
        
        # Nếu không thành công với bất kỳ credentials nào
        if not success:
            if last_error:
                raise last_error
            else:
                raise ValueError("Không thể xác thực với bất kỳ OAuth credentials nào")

    def _authorize_with_oauth(self, credentials_data):
        """Xác thực OAuth với một credentials cụ thể"""
        # Giải mã chuỗi Base64 và chuyển thành dict cấu hình
        client_config = json.loads(decode_base64(credentials_data).decode("utf-8"))
        scope = SCOPES
        creds = None
        
        # Xác định đường dẫn file token
        token_file = generate_token_path(credentials_data)
        
        # Kiểm tra token cũ trong file token_file
        if os.path.exists(token_file):
            try:
                with open(token_file, 'rb') as token:
                    creds = pickle.load(token)
            except Exception:
                # Nếu file token bị lỗi, bỏ qua
                creds = None
                if os.path.exists(token_file):
                    try:
                        os.remove(token_file)
                    except:
                        pass
        
        # Kiểm tra token cũ trong DEFAULT_TOKEN_FILE (cho tương thích ngược)
        if not creds and os.path.exists(DEFAULT_TOKEN_FILE):
            try:
                with open(DEFAULT_TOKEN_FILE, 'rb') as token:
                    creds = pickle.load(token)
            except Exception:
                # Nếu file token mặc định bị lỗi, bỏ qua
                creds = None
        
        # Nếu token không tồn tại hoặc không hợp lệ, thực hiện xác thực lại
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                try:
                    creds.refresh(Request())
                except RefreshError:
                    # Nếu token không thể refresh, xóa và xác thực lại
                    if os.path.exists(token_file):
                        os.remove(token_file)
                    flow = InstalledAppFlow.from_client_config(client_config, scope)
                    creds = flow.run_local_server(port=0)
            else:
                # Xác thực mới
                flow = InstalledAppFlow.from_client_config(client_config, scope)
                creds = flow.run_local_server(port=0)
            
            # Lưu token mới
            with open(token_file, 'wb') as token:
                pickle.dump(creds, token)
        
        # Thử kết nối với Google Sheets để xác nhận token có hiệu lực
        test_client = gspread.authorize(creds)
        # Thực hiện một thao tác đơn giản để kiểm tra quyền truy cập
        try:
            # Thử mở một spreadsheet công khai để xác nhận kết nối hoạt động
            test_client.open_by_key("1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms")
        except Exception as e:
            # Nếu token không có quyền truy cập, xóa token và thử lại
            if os.path.exists(token_file):
                os.remove(token_file)
            raise ValueError(f"Token không có quyền truy cập: {str(e)}")
        
        # Lưu client và creds khi xác thực thành công
        self.client = test_client
        self.creds = creds

    def open_by_key(self, sheet_id):
        return self.client.open_by_key(sheet_id)

    def get_sheets_service(self):
        """
        Trả về Google Sheets API service.
        """
        from googleapiclient.discovery import build
        return build('sheets', 'v4', credentials=self.creds)

    def get_drive_service(self):
        """
        Trả về Google Drive API service.
        """
        from googleapiclient.discovery import build
        return build('drive', 'v3', credentials=self.creds)
