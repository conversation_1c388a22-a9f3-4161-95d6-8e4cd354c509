#!/usr/bin/env python3
"""
Test script cho External Update module
"""

import sys
import os

# Thêm đường dẫn để import được các module
sys.path.insert(0, os.path.dirname(__file__))

def test_imports():
    """Test các import cần thiết"""
    try:
        print("Testing imports...")

        # Test import external_update module
        from programs.external_update import NAME, DESCRIPTION, ICON
        print(f"✅ External Update module: {NAME}")
        print(f"   Description: {DESCRIPTION}")
        print(f"   Icon: {ICON}")

        # Test import core logic
        from programs.external_update.core.external_logic import (
            ExternalUpdateProcessor,
            PROCESS_CONDITIONS,
            COLUMN_GROUPS,
            load_config,
            save_config
        )
        print("✅ Core logic imported successfully")

        # Test import routes
        from programs.external_update.routes import external_update_bp
        print("✅ Routes imported successfully")

        # Test process conditions
        print(f"✅ Process conditions loaded: {len(PROCESS_CONDITIONS)} conditions")
        for condition, info in list(PROCESS_CONDITIONS.items())[:3]:
            print(f"   - {condition}: {info['description']}")

        # Test column groups
        print(f"✅ Column groups loaded: {len(COLUMN_GROUPS)} groups")
        for group in list(COLUMN_GROUPS.keys())[:3]:
            print(f"   - {group}: {len(COLUMN_GROUPS[group])} columns")

        return True

    except Exception as e:
        print(f"❌ Import error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_config_functions():
    """Test các hàm cấu hình"""
    try:
        print("\nTesting config functions...")

        from programs.external_update.core.external_logic import load_config, save_config, PROCESS_CONDITIONS, COLUMN_GROUPS

        # Test load config
        config = load_config()
        if config:
            print("✅ Config loaded from file")
        else:
            print("ℹ️ No config file found (this is normal)")

        # Test save config
        test_conditions = {
            "test_condition": {
                "description": "Test condition for testing",
                "column_groups": ["price"]
            }
        }

        success = save_config(test_conditions, COLUMN_GROUPS)
        if success:
            print("✅ Config save test successful")
        else:
            print("⚠️ Config save test failed")

        return True

    except Exception as e:
        print(f"❌ Config test error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_processor_init():
    """Test khởi tạo processor"""
    try:
        print("\nTesting processor initialization...")

        from programs.external_update.core.external_logic import ExternalUpdateProcessor

        processor = ExternalUpdateProcessor()
        print("✅ ExternalUpdateProcessor initialized successfully")

        # Test các thuộc tính
        print(f"   - Batch size: {processor.batch_size}")
        print(f"   - Batch row limit: {processor.batch_row_limit}")
        print(f"   - Max retries: {processor.max_retries}")

        return True

    except Exception as e:
        print(f"❌ Processor init error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_gsheet_manager():
    """Test GoogleSheetManager riêng"""
    try:
        print("\nTesting GoogleSheetManager...")

        from programs.external_update.core.gsheet_manager import GoogleSheetManager
        from programs.external_update.core.external_logic import CREDENTIALS_BASE64

        # Test khởi tạo
        manager = GoogleSheetManager(CREDENTIALS_BASE64)
        print("✅ GoogleSheetManager initialized successfully")

        # Test decode credentials
        try:
            creds_info = manager._decode_credentials()
            print("✅ Credentials decoded successfully")
            print(f"   - Client ID: {creds_info.get('installed', {}).get('client_id', 'N/A')[:20]}...")
        except Exception as e:
            print(f"⚠️ Credentials decode test: {str(e)}")

        # Test token file path
        print(f"✅ Token file path: {manager.token_file}")

        return True

    except Exception as e:
        print(f"❌ GoogleSheetManager test error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧪 EXTERNAL UPDATE MODULE TEST")
    print("=" * 50)

    tests = [
        test_imports,
        test_config_functions,
        test_processor_init,
        test_gsheet_manager
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1
        print()

    print("=" * 50)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! External Update module is ready.")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
