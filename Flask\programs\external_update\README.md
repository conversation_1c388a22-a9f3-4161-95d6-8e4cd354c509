# 🔄 External Update

Chương trình cập nhật dữ liệu giữa Internal và External Google Sheets với nhiều điều kiện xử lý.

## 📋 Tính năng

- ✅ **Cập nhật dữ liệu tự động** gi<PERSON><PERSON> các Google Sheets
- ✅ **Nhiều điều kiện xử lý** (đổi giá, đổi stock, thêm quà, v.v.)
- ✅ **Batch processing** để xử lý dữ liệu lớn
- ✅ **Real-time progress tracking** 
- ✅ **Cấu hình linh hoạt** điều kiện xử lý
- ✅ **Error handling & retry** mạnh mẽ
- ✅ **PA action marker** tự động

## 🚀 Cài đặt

### 1. Dependencies
```bash
pip install -r requirements.txt
```

### 2. Google Sheets API Setup
- Chương trình sử dụng OAuth2 để kết nối Google Sheets
- Credentials đã được tích hợp sẵn
- Lần đầu sử dụng cần xác thực qua browser

## 📖 Cách sử dụng

### 1. <PERSON><PERSON><PERSON> cập chương trình
- Mở http://localhost:5000
- Chọn "🔄 External Update"

### 2. Cấu hình Sheets
- **Internal Data (Nguồn)**: Nhập URL Google Sheet chứa dữ liệu cần cập nhật
- **External Data (Đích)**: Nhập URL Google Sheet sẽ được cập nhật
- Click "Load" để tải danh sách worksheets
- Chọn worksheet tương ứng

### 3. Cấu hình xử lý
- **Dòng bắt đầu**: Để trống để tự động tìm "PA đã action đến đây"
- **Dòng header**: Dòng chứa tiêu đề cột (mặc định: 3)

### 4. Bắt đầu xử lý
- Click "Bắt đầu xử lý"
- Theo dõi tiến độ và logs real-time
- Chương trình sẽ tự động thêm marker "PA đã action đến đây"

## ⚙️ Cấu hình điều kiện xử lý

### Mở cấu hình
- Click nút "⚙️ Cấu hình" 
- Xem và chỉnh sửa các điều kiện xử lý

### Các điều kiện mặc định
- `đổi stock` - Thay đổi tồn kho
- `đổi giá` - Thay đổi giá sản phẩm  
- `thêm quà` / `đổi quà` / `xoá quà` - Quản lý quà tặng
- `cms` - Thay đổi hoa hồng
- `thêm voucher` - Thêm voucher
- Và nhiều điều kiện khác...

### Chỉnh sửa điều kiện
- Click "✏️" để chỉnh sửa điều kiện
- Thay đổi mô tả và chọn nhóm cột
- Click "💾" để lưu

### Thêm điều kiện mới
- Click "➕ Thêm điều kiện mới"
- Nhập tên điều kiện
- Cấu hình mô tả và nhóm cột
- Lưu cấu hình

## 📊 Nhóm cột

### `price` - Giá sản phẩm
- Giá bán Nhà bán hàng đề xuất

### `stock` - Tồn kho
- Số lượng tồn kho

### `gift` - Quà tặng
- Quà tặng kèm
- Mã quà tặng kèm
- Giá trị quà tặng kèm
- Link quà tặng kèm
- Số lượng tồn kho quà tặng

### `voucher` - Voucher
- % giảm giá của mã giảm giá
- Mức giảm tối đa
- Áp dụng cho đơn từ

### `commission` - Hoa hồng
- Tỷ lệ hoa hồng KOL

## 🔧 Cấu trúc thư mục

```
external_update/
├── __init__.py              # Program metadata
├── routes.py                # Flask routes & API
├── requirements.txt         # Dependencies
├── README.md               # Documentation
├── core/
│   ├── __init__.py
│   ├── external_logic.py   # Core processing logic
│   └── gsheet_manager.py   # Google Sheets manager
└── templates/
    └── external_update/
        └── index.html      # Web interface
```

## 🛠️ API Endpoints

- `GET /` - Giao diện chính
- `POST /api/validate_spreadsheet` - Kiểm tra quyền truy cập
- `POST /api/get_worksheets` - Lấy danh sách worksheets
- `GET /api/get_process_conditions` - Lấy cấu hình điều kiện
- `POST /api/save_process_conditions` - Lưu cấu hình điều kiện
- `POST /api/start_processing` - Bắt đầu xử lý
- `GET /api/get_processing_status` - Lấy trạng thái xử lý
- `POST /api/stop_processing` - Dừng xử lý
- `POST /api/clear_logs` - Xóa logs

## 📝 Logs & Debugging

- **Real-time logs** hiển thị trong giao diện
- **Progress tracking** với thanh tiến độ
- **Error handling** với retry mechanism
- **Detailed logging** cho debugging

## 🔒 Bảo mật

- **OAuth2** authentication với Google
- **Token storage** an toàn trong local directory
- **Automatic token refresh**
- **Isolated credentials** cho từng program

## 🤝 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra logs trong giao diện
2. Đảm bảo có quyền truy cập Google Sheets
3. Kiểm tra format dữ liệu trong sheets
4. Xem lại cấu hình điều kiện xử lý
