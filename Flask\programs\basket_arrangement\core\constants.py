"""
Constants và cấu hình cho Basket Arrangement
Copy đầy đủ từ basket_package.py để đảm bảo tính toàn vẹn 100%
"""

"""
THÔNG TIN THUẬT TOÁN SẮP XẾP MỚI (v2.0)
=====================================
Thuật toán sắp xếp ID có quy tắc ưu tiên như sau:

1. THỨ TỰ NO LÀ "BẤT KHẢ XÂM PHẠM"
   - ID có NO 1 luôn được xếp đầu tiên
   - ID có NO 2 luôn xếp sau NO 1
   - ID có NO 3 luôn xếp sau NO 2
   ... và tiếp tục theo thứ tự

2. QUY TẮC ƯU TIÊN:
   - Trong cùng một NO, ID có review được xếp trước ID không có review
   - Trong cùng một NO và cùng trạng thái review, sắp xếp theo GMV giảm dần

3. ĐẢM BẢO NO VÀ REVIEW:
   - Không bao giờ phá vỡ thứ tự NO dù có bất kỳ input nào
   - ID input có thể chen vào nhưng không được phép phá vỡ thứ tự NO
   - ID exclusive được giữ nguyên vị trí

4. QUY TẮC XỬ LÝ XÓA ID DƯ THỪA:
   - Chỉ xóa những ID không thuộc timeline đang xử lý
   - Ưu tiên xóa những ID có GMV thấp nhất

5. MÃ MÀU:
   - ✓ : Thành công - ID được sắp xếp đúng thứ tự theo NO
   - ⚠️ : Cảnh báo - Phát hiện vấn đề trong sắp xếp ID theo NO
"""

# Thêm các hằng số mới
MAX_UNIQUE_IDS = 500  # Số lượng ID tối đa cho mỗi cột

# Các hằng số cho Basket sheet
DEFAULT_SHEET_NAME = 'Basket'
NUM_ID_COLUMNS = 12    # Số lượng cột ID
COLUMN_STEP = 5        # Khoảng cách giữa các cột ID
TIME_SLOT_ROW = 2      # Khung giờ (14:00-15:00) nằm ở dòng 2
HOUR_NAME_ROW = 2      # Tên giờ (Giờ 3) cũng nằm ở dòng 2
HEADER_ROW = 3         # Header (Shop ID, Item ID, STT) nằm ở dòng 3
DATA_START_ROW = 4     # Dữ liệu bắt đầu từ dòng 4

# Các hằng số cho Deal list sheet
DEAL_LIST_HEADER_ROW = 2      # Header nằm ở dòng 2 (index 1)
DEAL_LIST_DATA_START_ROW = 4  # Dữ liệu bắt đầu từ dòng 4 (index 3)

# Các hằng số cho Smart Random Placement
MIN_DISTANCE_BETWEEN_INPUT_IDS = 2  # Khoảng cách tối thiểu giữa các ID input
PLACEMENT_PRIORITY_WEIGHTS = {
    'EMPTY_SLOT': 0.7,      # 70% ưu tiên vị trí trống
    'BOUNDARY': 0.2,        # 20% ưu tiên vị trí boundary
    'GROUP_EDGE': 0.08,     # 8% ưu tiên vị trí đầu/cuối nhóm
    'GROUP_MIDDLE': 0.02    # 2% ưu tiên vị trí giữa nhóm (cuối cùng)
}

# BASE64 OAuth2 credentials
BASE64_OAUTH = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

# Column helpers
def col_to_index(col):
    result = 0
    for c in col:
        result = result * 26 + (ord(c.upper()) - ord('A') + 1)
    return result - 1

def index_to_col(index):
    col = ''
    while index >= 0:
        col = chr(index % 26 + ord('A')) + col
        index = index // 26 - 1
    return col

# Thêm hàm chuẩn hóa timeline ngay sau các hàm col_to_index và index_to_col
def normalize_timeline(timeline):
    """Chuẩn hóa timeline thành dạng không có dấu đặc biệt và khoảng trắng
    Ví dụ: '12:00-13:00' -> '********'
    """
    if not timeline:
        return ""
    # Loại bỏ dấu :, dấu - và khoảng trắng
    return timeline.replace(":", "").replace("-", "").replace(" ", "").lower()

# Các hằng số cho MainApp
DEFAULT_COLUMN_MAPPING = {
    "id_column": "C",
    "cluster_column": "I",
    "nmv_column": "AZ",
    "timeline_column": "R",  # Cột Timeline
    "shop_id_column": "F",   # Cột Shop ID
    "review_m_column": "M",  # Cột Review M
    "review_n_column": "N",  # Cột Review N
    "no_column": "S"         # Cột NO (thứ tự ưu tiên)
}

# Các hằng số cho DealListManager
DEFAULT_DEAL_LIST_MAPPING = {
    "id_column": "A",
    "cluster_column": "I",
    "nmv_column": "AC",
    "timeline_column": "R",  # Thêm cột Timeline mặc định
    "shop_id_column": "F",   # Thêm cột Shop ID mặc định
    "review_m_column": "M",  # Thêm cột Review M mặc định
    "review_n_column": "N",  # Thêm cột Review N mặc định
    "no_column": "S"         # Thêm cột NO mặc định
}

# Các thông báo debug
DEBUG_MESSAGES = {
    'FIXED_ISSUES': [
        "1. Đã sửa cấu trúc cột cho mỗi khung giờ: Shop ID (A/F/K...), Item ID (B/G/L...), STT (C/H/M...), Brand (D/I/N...)",
        "2. Đã sửa cơ chế đọc dữ liệu Shop ID từ cùng cột với khung giờ nhưng bắt đầu từ dòng 4",
        "3. Đã nâng cao độ chính xác khi ánh xạ khung giờ với các cột liên quan"
    ]
}

# Các pattern regex cho validation
REGEX_PATTERNS = {
    'SHEET_ID_PATTERNS': [
        r'/spreadsheets/d/([a-zA-Z0-9-_]+)',
        r'key=([a-zA-Z0-9-_]+)',
        r'^([a-zA-Z0-9-_]+)$'
    ],
    'ID_SEPARATORS': r'[\t\r\n;]+',
    'MULTIPLE_SPACES': r'\s{2,}',
    'SINGLE_SPACE_BETWEEN_WORDS': r'(\S+)\s+(\S+)',
    'MULTIPLE_COMMAS': r',+',
    'COMMA_WITH_SPACES': r'\s*,\s*'
}

# Các cấu hình UI (từ PyQt6 gốc)
UI_CONFIG = {
    'WINDOW_TITLE': "Random ID to Google Sheet",
    'WINDOW_SIZE': (1000, 650),
    'INPUT_PLACEHOLDER': "Nhập hoặc dán các ID (tự động nhận diện định dạng)",
    'TOOLTIP_TEXT': "Hỗ trợ paste nhiều ID từ nhiều nguồn:\n"
                   "- Phân cách bởi dấu phẩy, chấm phẩy, tab, xuống dòng, khoảng trắng\n"
                   "- Tự động dọn dẹp ký tự thừa\n"
                   "- Tự động định dạng lại thành danh sách phân cách bởi dấu phẩy"
}

# Các cấu hình cho TimeSlotProcessor
TIME_SLOT_CONFIG = {
    'AVOID_POSITIONS': {
        20: 3,
        30: 6,
        50: 10,
        100: 20,
        150: 30,
        200: 100,
        250: 120
    },
    'DEFAULT_AVOID_RATIO': 5  # top_limit // 5
}

# Các cấu hình cho validation
VALIDATION_CONFIG = {
    'MAX_ERRORS_TO_SHOW': 5,
    'DEFAULT_NO_VALUE': 999999,
    'DEFAULT_GMV_VALUE': 0.0
}

# Các message templates
MESSAGE_TEMPLATES = {
    'SMART_PLACEMENT_START': "=== BẮT ĐẦU SMART PLACEMENT CHO {count} ID INPUT ===",
    'STRUCTURE_ANALYSIS': "Phân tích cấu trúc:",
    'EMPTY_POSITIONS': "  - Vị trí trống: {count}",
    'BOUNDARY_POSITIONS': "  - Vị trí boundary: {count}",
    'GROUP_EDGE_POSITIONS': "  - Vị trí group edge: {count}",
    'GROUP_MIDDLE_POSITIONS': "  - Vị trí group middle: {count}",
    'ID_CLASSIFICATION': "Phân loại ID input:",
    'GROUPED_IDS': "  - ID nhóm: {count}",
    'NON_GROUPED_IDS': "  - ID không nhóm: {count}",
    'PROCESSING_GROUPED': "Xử lý {count} ID nhóm (sử dụng thuật toán cũ)",
    'PROCESSING_NON_GROUPED': "Xử lý {count} ID không nhóm với Smart Placement",
    'SMART_PLACEMENT_END': "=== KẾT THÚC SMART PLACEMENT ===",
    'VALIDATION_ISSUES': "⚠️ Phát hiện {count} vấn đề trong danh sách ID",
    'NO_ORDER_MAINTAINED': "✅ Thứ tự NO được duy trì đúng",
    'NO_ORDER_VIOLATED': "⚠️ Phát hiện vi phạm thứ tự NO",
    'SORTING_SUCCESS': "✅ Đã sắp xếp {count} ID theo NO và Review",
    'AUTO_FIX_NO_ORDER': "🔧 Đang sửa chữa thứ tự NO..."
}
