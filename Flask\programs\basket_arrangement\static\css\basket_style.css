/* Basket Arrangement Specific Styles */

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.modal-content {
    background: #2d2d2d;
    border: 1px solid #555;
    border-radius: 8px;
    padding: 20px;
    max-width: 500px;
    width: 90%;
    color: #fff;
}

.modal-content h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #fff;
    text-align: center;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #333;
    border-radius: 10px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #0d6efd, #198754);
    width: 0%;
    transition: width 0.3s ease;
}

.status-panel {
    background: #1a1a1a;
    border: 1px solid #555;
    border-radius: 5px;
    padding: 15px;
    margin: 15px 0;
}

.status-item {
    display: flex;
    align-items: center;
    margin: 8px 0;
    padding: 5px;
    border-radius: 3px;
}

.status-item.success {
    background: rgba(40, 167, 69, 0.1);
    border-left: 3px solid #28a745;
}

.status-item.error {
    background: rgba(220, 53, 69, 0.1);
    border-left: 3px solid #dc3545;
}

.status-item.warning {
    background: rgba(255, 193, 7, 0.1);
    border-left: 3px solid #ffc107;
}

.status-item.info {
    background: rgba(13, 110, 253, 0.1);
    border-left: 3px solid #0d6efd;
}

.advanced-controls {
    background: #2a2a2a;
    border: 1px solid #555;
    border-radius: 5px;
    padding: 15px;
    margin: 15px 0;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 15px;
    margin: 10px 0;
}

.control-group label {
    min-width: 120px;
    color: #ddd;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #555;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #0d6efd;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 15px 0;
}

.stat-card {
    background: #2a2a2a;
    border: 1px solid #555;
    border-radius: 5px;
    padding: 15px;
    text-align: center;
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #0d6efd;
    margin-bottom: 5px;
}

.stat-label {
    color: #aaa;
    font-size: 12px;
}

.action-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin: 20px 0;
}

.btn-group {
    display: flex;
    gap: 5px;
}

.btn-icon {
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.btn-icon::before {
    font-size: 16px;
}

.btn-load::before { content: "📊"; }
.btn-process::before { content: "🚀"; }
.btn-save::before { content: "💾"; }
.btn-clear::before { content: "🗑️"; }
.btn-settings::before { content: "⚙️"; }

.collapsible {
    background-color: #2a2a2a;
    color: white;
    cursor: pointer;
    padding: 15px;
    width: 100%;
    border: 1px solid #555;
    text-align: left;
    outline: none;
    border-radius: 5px;
    margin: 5px 0;
    transition: background-color 0.3s;
}

.collapsible:hover {
    background-color: #3a3a3a;
}

.collapsible.active {
    background-color: #0d6efd;
}

.collapsible-content {
    padding: 0 15px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
    background-color: #1a1a1a;
    border: 1px solid #555;
    border-top: none;
    border-radius: 0 0 5px 5px;
}

.collapsible-content.active {
    padding: 15px;
    max-height: 500px;
}

.highlight {
    background-color: rgba(255, 193, 7, 0.2);
    border: 1px solid #ffc107;
    border-radius: 3px;
    padding: 2px 4px;
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.loading-spinner {
    border: 3px solid #333;
    border-top: 3px solid #0d6efd;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 5px;
    color: white;
    z-index: 3000;
    max-width: 300px;
    animation: slideInRight 0.3s ease-out;
}

.notification.success {
    background-color: #28a745;
}

.notification.error {
    background-color: #dc3545;
}

.notification.warning {
    background-color: #ffc107;
    color: #000;
}

.notification.info {
    background-color: #0d6efd;
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

.drag-drop-area {
    border: 2px dashed #555;
    border-radius: 5px;
    padding: 20px;
    text-align: center;
    color: #aaa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.drag-drop-area:hover,
.drag-drop-area.dragover {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
}

.file-input {
    display: none;
}

.context-menu {
    position: absolute;
    background: #2d2d2d;
    border: 1px solid #555;
    border-radius: 4px;
    padding: 5px 0;
    z-index: 1000;
    min-width: 150px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.context-menu-item {
    padding: 8px 15px;
    cursor: pointer;
    color: #fff;
    transition: background-color 0.2s;
}

.context-menu-item:hover {
    background-color: #3a3a3a;
}

.context-menu-separator {
    height: 1px;
    background-color: #555;
    margin: 5px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .basket-container {
        padding: 10px;
    }
    
    .form-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .form-row label {
        min-width: auto;
        margin-bottom: 5px;
    }
    
    .condition-row {
        flex-direction: column;
        gap: 10px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}
