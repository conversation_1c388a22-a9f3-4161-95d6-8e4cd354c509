<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔄 External Update - Data Assortment All in One</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .program-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }

        .form-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .section-title {
            color: #667eea;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
            margin-bottom: 1rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-1px);
        }

        .log-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            height: 400px;
            overflow-y: auto;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
        }

        .log-info { background-color: #e3f2fd; }
        .log-success { background-color: #e8f5e8; }
        .log-warning { background-color: #fff3cd; }
        .log-error { background-color: #f8d7da; }

        .progress-container {
            margin: 1rem 0;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .status-idle { background-color: #6c757d; }
        .status-processing { background-color: #ffc107; animation: pulse 1s infinite; }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .worksheet-select {
            min-height: 38px;
        }

        .input-group-text {
            background-color: #667eea;
            color: white;
            border-color: #667eea;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }


    </style>
</head>
<body>


    <div class="program-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-exchange-alt"></i> External Update</h1>
                    <p class="mb-0">Cập nhật dữ liệu giữa Internal và External Google Sheets</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex align-items-center justify-content-end">
                        <span class="status-indicator" id="statusIndicator"></span>
                        <span id="statusText">Sẵn sàng</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Cấu hình Sheets -->
        <div class="row">
            <div class="col-md-6">
                <div class="form-section">
                    <h4 class="section-title"><i class="fas fa-file-import"></i> Internal Data (Nguồn)</h4>
                    <div class="mb-3">
                        <label class="form-label">URL Google Sheet:</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-link"></i></span>
                            <input type="text" class="form-control" id="sourceUrl" placeholder="Nhập URL hoặc ID của Google Sheet">
                            <button class="btn btn-outline-secondary" type="button" id="loadSourceBtn">
                                <i class="fas fa-sync"></i> Load
                            </button>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Worksheet:</label>
                        <select class="form-select worksheet-select" id="sourceWorksheet">
                            <option value="">Chọn worksheet...</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Dòng bắt đầu xử lý:</label>
                        <input type="number" class="form-control" id="startRow" placeholder="Để trống để tự động tìm">
                        <div class="form-text">Để trống để tự động tìm dòng "PA đã action đến đây"</div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="form-section">
                    <h4 class="section-title"><i class="fas fa-file-export"></i> External Data (Đích)</h4>
                    <div class="mb-3">
                        <label class="form-label">URL Google Sheet:</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-link"></i></span>
                            <input type="text" class="form-control" id="targetUrl" placeholder="Nhập URL hoặc ID của Google Sheet">
                            <button class="btn btn-outline-secondary" type="button" id="loadTargetBtn">
                                <i class="fas fa-sync"></i> Load
                            </button>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Worksheet:</label>
                        <select class="form-select worksheet-select" id="targetWorksheet">
                            <option value="">Chọn worksheet...</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Dòng chứa header:</label>
                        <input type="number" class="form-control" id="headerRow" value="3" min="1">
                        <div class="form-text">Dòng chứa tiêu đề cột (mặc định: 3)</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Nút điều khiển -->
        <div class="form-section">
            <div class="row">
                <div class="col-md-8">
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary" id="startProcessBtn">
                            <i class="fas fa-play"></i> Bắt đầu xử lý
                        </button>
                        <button class="btn btn-warning" id="stopProcessBtn" disabled>
                            <i class="fas fa-stop"></i> Dừng xử lý
                        </button>
                        <button class="btn btn-secondary" id="clearLogsBtn">
                            <i class="fas fa-trash"></i> Xóa logs
                        </button>
                        <button class="btn btn-info" id="configBtn">
                            <i class="fas fa-cog"></i> Cấu hình
                        </button>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="progress-container">
                        <div class="progress">
                            <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%">0%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Logs -->
        <div class="form-section">
            <h4 class="section-title"><i class="fas fa-list"></i> Processing Logs</h4>
            <div class="log-container" id="logContainer">
                <div class="text-muted">Chờ bắt đầu xử lý...</div>
            </div>
        </div>
    </div>

    <!-- Modal cấu hình -->
    <div class="modal fade" id="configModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-cog"></i> Cấu hình điều kiện xử lý</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="configContent">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                    <button type="button" class="btn btn-primary" id="saveConfigBtn">Lưu cấu hình</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global variables
        let processingInterval = null;
        let currentConfig = null;

        // DOM elements
        const sourceUrl = document.getElementById('sourceUrl');
        const targetUrl = document.getElementById('targetUrl');
        const sourceWorksheet = document.getElementById('sourceWorksheet');
        const targetWorksheet = document.getElementById('targetWorksheet');
        const startRow = document.getElementById('startRow');
        const headerRow = document.getElementById('headerRow');
        const loadSourceBtn = document.getElementById('loadSourceBtn');
        const loadTargetBtn = document.getElementById('loadTargetBtn');
        const startProcessBtn = document.getElementById('startProcessBtn');
        const stopProcessBtn = document.getElementById('stopProcessBtn');
        const clearLogsBtn = document.getElementById('clearLogsBtn');
        const configBtn = document.getElementById('configBtn');
        const progressBar = document.getElementById('progressBar');
        const logContainer = document.getElementById('logContainer');
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        const configModal = new bootstrap.Modal(document.getElementById('configModal'));
        const saveConfigBtn = document.getElementById('saveConfigBtn');

        // Utility functions
        function showToast(message, type = 'info') {
            // Simple toast implementation
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} position-fixed top-0 end-0 m-3`;
            toast.style.zIndex = '9999';
            toast.textContent = message;
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 3000);
        }

        function updateStatus(status, text) {
            statusIndicator.className = `status-indicator status-${status}`;
            statusText.textContent = text;
        }

        function addLog(entry) {
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${entry.level.toLowerCase()}`;
            logEntry.innerHTML = `<strong>[${entry.timestamp}]</strong> ${entry.message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLogs() {
            logContainer.innerHTML = '<div class="text-muted">Logs đã được xóa...</div>';
        }

        // API functions
        async function validateSpreadsheet(url) {
            try {
                const response = await fetch('/external_update/api/validate_spreadsheet', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ spreadsheet_url: url })
                });
                return await response.json();
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function getWorksheets(url) {
            try {
                const response = await fetch('/external_update/api/get_worksheets', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ spreadsheet_url: url })
                });
                return await response.json();
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // Event handlers
        loadSourceBtn.addEventListener('click', async () => {
            const url = sourceUrl.value.trim();
            if (!url) {
                showToast('Vui lòng nhập URL Google Sheet', 'warning');
                return;
            }

            loadSourceBtn.disabled = true;
            loadSourceBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';

            try {
                const result = await getWorksheets(url);
                if (result.success) {
                    sourceWorksheet.innerHTML = '<option value="">Chọn worksheet...</option>';
                    result.worksheets.forEach(ws => {
                        const option = document.createElement('option');
                        option.value = ws.title;
                        option.textContent = ws.title;
                        sourceWorksheet.appendChild(option);
                    });
                    showToast('Đã tải danh sách worksheet thành công', 'success');
                } else {
                    showToast(result.error, 'danger');
                }
            } catch (error) {
                showToast('Lỗi khi tải worksheets: ' + error.message, 'danger');
            } finally {
                loadSourceBtn.disabled = false;
                loadSourceBtn.innerHTML = '<i class="fas fa-sync"></i> Load';
            }
        });

        loadTargetBtn.addEventListener('click', async () => {
            const url = targetUrl.value.trim();
            if (!url) {
                showToast('Vui lòng nhập URL Google Sheet', 'warning');
                return;
            }

            loadTargetBtn.disabled = true;
            loadTargetBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';

            try {
                const result = await getWorksheets(url);
                if (result.success) {
                    targetWorksheet.innerHTML = '<option value="">Chọn worksheet...</option>';
                    result.worksheets.forEach(ws => {
                        const option = document.createElement('option');
                        option.value = ws.title;
                        option.textContent = ws.title;
                        targetWorksheet.appendChild(option);
                    });
                    showToast('Đã tải danh sách worksheet thành công', 'success');
                } else {
                    showToast(result.error, 'danger');
                }
            } catch (error) {
                showToast('Lỗi khi tải worksheets: ' + error.message, 'danger');
            } finally {
                loadTargetBtn.disabled = false;
                loadTargetBtn.innerHTML = '<i class="fas fa-sync"></i> Load';
            }
        });

        startProcessBtn.addEventListener('click', async () => {
            // Validate inputs
            if (!sourceUrl.value.trim() || !targetUrl.value.trim() ||
                !sourceWorksheet.value || !targetWorksheet.value) {
                showToast('Vui lòng điền đầy đủ thông tin', 'warning');
                return;
            }

            const data = {
                source_url: sourceUrl.value.trim(),
                target_url: targetUrl.value.trim(),
                source_sheet: sourceWorksheet.value,
                target_sheet: targetWorksheet.value,
                start_row: startRow.value ? parseInt(startRow.value) : null,
                header_row: parseInt(headerRow.value) || 3
            };

            try {
                const response = await fetch('/external_update/api/start_processing', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                const result = await response.json();

                if (result.success) {
                    startProcessBtn.disabled = true;
                    stopProcessBtn.disabled = false;
                    updateStatus('processing', 'Đang xử lý...');
                    clearLogs();
                    startPolling();
                    showToast('Đã bắt đầu xử lý dữ liệu', 'success');
                } else {
                    showToast(result.error, 'danger');
                }
            } catch (error) {
                showToast('Lỗi khi bắt đầu xử lý: ' + error.message, 'danger');
            }
        });

        stopProcessBtn.addEventListener('click', async () => {
            try {
                const response = await fetch('/external_update/api/stop_processing', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const result = await response.json();

                if (result.success) {
                    stopPolling();
                    startProcessBtn.disabled = false;
                    stopProcessBtn.disabled = true;
                    updateStatus('idle', 'Đã dừng');
                    showToast('Đã dừng xử lý', 'warning');
                } else {
                    showToast(result.error, 'danger');
                }
            } catch (error) {
                showToast('Lỗi khi dừng xử lý: ' + error.message, 'danger');
            }
        });

        clearLogsBtn.addEventListener('click', async () => {
            try {
                const response = await fetch('/external_update/api/clear_logs', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const result = await response.json();

                if (result.success) {
                    clearLogs();
                    showToast('Đã xóa logs', 'info');
                } else {
                    showToast(result.error, 'danger');
                }
            } catch (error) {
                showToast('Lỗi khi xóa logs: ' + error.message, 'danger');
            }
        });

        configBtn.addEventListener('click', async () => {
            try {
                const response = await fetch('/external_update/api/get_process_conditions');
                const result = await response.json();

                if (result.success) {
                    currentConfig = result;
                    displayConfig(result);
                    configModal.show();
                } else {
                    showToast(result.error, 'danger');
                }
            } catch (error) {
                showToast('Lỗi khi tải cấu hình: ' + error.message, 'danger');
            }
        });

        saveConfigBtn.addEventListener('click', async () => {
            if (!currentConfig) return;

            try {
                const response = await fetch('/external_update/api/save_process_conditions', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(currentConfig)
                });
                const result = await response.json();

                if (result.success) {
                    showToast('Đã lưu cấu hình thành công', 'success');
                    configModal.hide();
                } else {
                    showToast(result.error, 'danger');
                }
            } catch (error) {
                showToast('Lỗi khi lưu cấu hình: ' + error.message, 'danger');
            }
        });

        function displayConfig(config) {
            const content = document.getElementById('configContent');
            let html = '<div class="row">';

            // Display process conditions with edit capability
            html += '<div class="col-12"><h6>Điều kiện xử lý:</h6>';
            html += '<div class="mb-3">';
            html += '<button class="btn btn-sm btn-success" onclick="addNewCondition()"><i class="fas fa-plus"></i> Thêm điều kiện mới</button>';
            html += '</div>';
            html += '<div class="table-responsive"><table class="table table-sm table-bordered">';
            html += '<thead class="table-light"><tr><th>Điều kiện</th><th>Mô tả</th><th>Nhóm cột</th><th>Thao tác</th></tr></thead><tbody id="conditionsTableBody">';

            for (const [condition, info] of Object.entries(config.process_conditions)) {
                html += `<tr data-condition="${condition}">
                    <td><input type="text" class="form-control form-control-sm condition-name" value="${condition}" readonly></td>
                    <td><input type="text" class="form-control form-control-sm condition-desc" value="${info.description}"></td>
                    <td>
                        <select class="form-select form-select-sm condition-groups" multiple>`;

                // Add options for column groups
                for (const [groupName, groupColumns] of Object.entries(config.column_groups)) {
                    const selected = info.column_groups.includes(groupName) ? 'selected' : '';
                    html += `<option value="${groupName}" ${selected}>${groupName}</option>`;
                }

                html += `</select>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="editCondition('${condition}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteCondition('${condition}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>`;
            }

            html += '</tbody></table></div></div>';

            // Display column groups (read-only for now)
            html += '<div class="col-12 mt-3"><h6>Nhóm cột (chỉ đọc):</h6>';
            html += '<div class="row">';

            for (const [group, columns] of Object.entries(config.column_groups)) {
                html += `<div class="col-md-6 mb-2">
                    <div class="card card-body p-2">
                        <strong class="text-primary">${group}:</strong>
                        <ul class="small mb-0 mt-1">`;
                columns.forEach(col => {
                    html += `<li>${col}</li>`;
                });
                html += '</ul></div></div>';
            }

            html += '</div></div></div>';
            content.innerHTML = html;
        }

        function addNewCondition() {
            const tbody = document.getElementById('conditionsTableBody');
            const newConditionName = prompt('Nhập tên điều kiện mới:');
            if (!newConditionName || newConditionName.trim() === '') return;

            const conditionName = newConditionName.trim().toLowerCase();

            // Check if condition already exists
            if (currentConfig.process_conditions[conditionName]) {
                alert('Điều kiện này đã tồn tại!');
                return;
            }

            // Add to config
            currentConfig.process_conditions[conditionName] = {
                description: 'Mô tả mới',
                column_groups: []
            };

            // Refresh display
            displayConfig(currentConfig);
        }

        function editCondition(conditionName) {
            const row = document.querySelector(`tr[data-condition="${conditionName}"]`);
            const nameInput = row.querySelector('.condition-name');
            const descInput = row.querySelector('.condition-desc');
            const groupsSelect = row.querySelector('.condition-groups');

            if (nameInput.readOnly) {
                // Enable editing
                nameInput.readOnly = false;
                nameInput.classList.add('border-warning');
                descInput.classList.add('border-warning');
                groupsSelect.classList.add('border-warning');

                // Change button to save
                const editBtn = row.querySelector('.btn-outline-primary');
                editBtn.innerHTML = '<i class="fas fa-save"></i>';
                editBtn.onclick = () => saveCondition(conditionName, row);
            }
        }

        function saveCondition(oldConditionName, row) {
            const nameInput = row.querySelector('.condition-name');
            const descInput = row.querySelector('.condition-desc');
            const groupsSelect = row.querySelector('.condition-groups');

            const newConditionName = nameInput.value.trim().toLowerCase();
            const newDescription = descInput.value.trim();
            const selectedGroups = Array.from(groupsSelect.selectedOptions).map(option => option.value);

            if (!newConditionName || !newDescription) {
                alert('Vui lòng điền đầy đủ thông tin!');
                return;
            }

            // Update config
            if (oldConditionName !== newConditionName) {
                // Remove old condition
                delete currentConfig.process_conditions[oldConditionName];
            }

            currentConfig.process_conditions[newConditionName] = {
                description: newDescription,
                column_groups: selectedGroups
            };

            // Refresh display
            displayConfig(currentConfig);
        }

        function deleteCondition(conditionName) {
            if (confirm(`Bạn có chắc muốn xóa điều kiện "${conditionName}"?`)) {
                delete currentConfig.process_conditions[conditionName];
                displayConfig(currentConfig);
            }
        }

        function startPolling() {
            processingInterval = setInterval(async () => {
                try {
                    const response = await fetch('/external_update/api/get_processing_status');
                    const result = await response.json();

                    if (result.success) {
                        const status = result.status;

                        // Update progress
                        progressBar.style.width = status.progress + '%';
                        progressBar.textContent = status.progress + '%';

                        // Add new logs
                        if (status.logs && status.logs.length > 0) {
                            const currentLogCount = logContainer.children.length;
                            for (let i = currentLogCount; i < status.logs.length; i++) {
                                addLog(status.logs[i]);
                            }
                        }

                        // Check if processing finished
                        if (!status.is_processing) {
                            stopPolling();
                            startProcessBtn.disabled = false;
                            stopProcessBtn.disabled = true;

                            if (status.error) {
                                updateStatus('error', 'Lỗi xử lý');
                                showToast('Xử lý thất bại: ' + status.error, 'danger');
                            } else if (status.result) {
                                updateStatus('success', 'Hoàn thành');
                                showToast('Xử lý hoàn thành thành công', 'success');
                            }
                        }
                    }
                } catch (error) {
                    console.error('Error polling status:', error);
                }
            }, 1000);
        }

        function stopPolling() {
            if (processingInterval) {
                clearInterval(processingInterval);
                processingInterval = null;
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            updateStatus('idle', 'Sẵn sàng');
        });
    </script>
</body>
</html>
