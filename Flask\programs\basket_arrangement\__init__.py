from flask import Blueprint, render_template, request, jsonify, session
import traceback

from .logic import EnhancedBasketArrangementLogic

# Thông tin chương trình
NAME = "Basket Arrangement"
DESCRIPTION = "Sắp xếp ID thông minh vào Google Sheet với thuật toán tối ưu"
ICON = "🛒"

# Tạo Blueprint
basket_bp = Blueprint('basket_arrangement', __name__,
                     template_folder='templates',
                     static_folder='static',
                     url_prefix='/basket_arrangement')

def setup(app):
    """Đăng ký blueprint với Flask app"""
    app.register_blueprint(basket_bp)

@basket_bp.route('/')
def index():
    """Trang chính của Basket Arrangement"""
    return render_template('programs/basket_arrangement.html')

@basket_bp.route('/api/load_sheet', methods=['POST'])
def load_sheet():
    """API để load Google Sheet"""
    try:
        data = request.get_json()
        sheet_url = data.get('sheet_url', '').strip()

        if not sheet_url:
            return jsonify({
                'success': False,
                'error': 'URL Google Sheet không được để trống'
            })

        # Sử dụng logic thực để load sheet
        from .logic import EnhancedBasketArrangementLogic

        logic = EnhancedBasketArrangementLogic()

        try:
            # Load spreadsheet thực
            result = logic.load_spreadsheet(sheet_url)

            # Lưu thông tin vào session
            session['basket_arrangement'] = {
                'sheet_id': result['sheet_id'],
                'sheet_names': result['sheet_names'],
                'spreadsheet_title': f"Spreadsheet ({result['sheet_id'][:8]}...)",
                'is_real_connection': True
            }
            session.modified = True

            return jsonify({
                'success': True,
                'data': {
                    'sheet_id': result['sheet_id'],
                    'sheet_names': result['sheet_names'],
                    'total_sheets': result['total_sheets'],
                    'spreadsheet_title': session['basket_arrangement']['spreadsheet_title']
                }
            })

        except Exception as e:
            # Fallback với mock data nếu không kết nối được
            mock_sheet_names = ['Basket', 'Deal list', 'Summary', 'Config']

            # Parse sheet ID từ URL để có thông tin cơ bản
            import re
            sheet_id = None
            patterns = [
                r'/spreadsheets/d/([a-zA-Z0-9-_]+)',
                r'id=([a-zA-Z0-9-_]+)',
                r'^([a-zA-Z0-9-_]+)$'
            ]

            for pattern in patterns:
                match = re.search(pattern, sheet_url)
                if match:
                    sheet_id = match.group(1)
                    break

            if not sheet_id:
                sheet_id = 'unknown_sheet'

            # Lưu thông tin mock vào session
            session['basket_arrangement'] = {
                'sheet_id': sheet_id,
                'sheet_names': mock_sheet_names,
                'spreadsheet_title': f'Mock Spreadsheet ({sheet_id[:8]}...)',
                'is_real_connection': False,
                'connection_error': str(e)
            }
            session.modified = True

            return jsonify({
                'success': True,
                'data': {
                    'sheet_id': sheet_id,
                    'sheet_names': mock_sheet_names,
                    'total_sheets': len(mock_sheet_names),
                    'spreadsheet_title': session['basket_arrangement']['spreadsheet_title']
                },
                'warning': f'Sử dụng mock data do lỗi kết nối: {str(e)}'
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

@basket_bp.route('/api/get_time_slots', methods=['POST'])
def get_time_slots():
    """API để lấy danh sách khung giờ từ sheet"""
    try:
        # Temporary simplified response for testing
        return jsonify({
            'success': True,
            'time_slots': ['14:00-15:00', '15:00-16:00', '16:00-17:00', '17:00-18:00']
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@basket_bp.route('/api/process_arrangement', methods=['POST'])
def process_arrangement():
    """API để xử lý sắp xếp ID"""
    try:
        data = request.get_json()

        # Lấy thông tin từ request
        sheet_url = data.get('sheet_url', '').strip()
        worksheet_name = data.get('worksheet_name', '').strip()
        conditions = data.get('conditions', [])

        if not sheet_url:
            return jsonify({
                'success': False,
                'error': 'URL Google Sheet không được để trống'
            })

        if not worksheet_name:
            return jsonify({
                'success': False,
                'error': 'Tên worksheet không được để trống'
            })

        if not conditions:
            return jsonify({
                'success': False,
                'error': 'Cần có ít nhất một điều kiện sắp xếp'
            })

        # Khởi tạo logic xử lý
        logic = EnhancedBasketArrangementLogic()

        # Xử lý sắp xếp
        result = logic.process_arrangement(sheet_url, worksheet_name, conditions)

        if result.get('success'):
            return jsonify({
                'success': True,
                'result': result
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('error', 'Lỗi không xác định')
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

@basket_bp.route('/api/get_column_mapping', methods=['GET'])
def get_column_mapping():
    """API để lấy thông tin mapping cột"""
    try:
        # Lấy mapping từ session hoặc sử dụng default
        mapping = session.get('basket_logic', {}).get('column_mapping', {
            'shop_id': 'A',
            'item_id': 'B',
            'gmv': 'C',
            'review': 'D',
            'no': 'E'
        })

        return jsonify({
            'success': True,
            'mapping': mapping
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@basket_bp.route('/api/update_column_mapping', methods=['POST'])
def update_column_mapping():
    """API để cập nhật mapping cột"""
    try:
        data = request.get_json()
        mapping = data.get('mapping', {})

        # Lưu mapping vào session
        if 'basket_logic' not in session:
            session['basket_logic'] = {}
        session['basket_logic']['column_mapping'] = mapping
        session.modified = True

        return jsonify({
            'success': True,
            'message': 'Đã cập nhật mapping cột thành công'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
