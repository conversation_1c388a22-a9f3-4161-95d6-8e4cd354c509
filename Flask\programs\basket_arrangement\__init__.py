from flask import Blueprint, render_template, request, jsonify, session
import traceback

# Temporarily comment out complex logic import to test basic functionality
# from .logic import EnhancedBasketArrangementLogic

# Thông tin chương trình
NAME = "Basket Arrangement"
DESCRIPTION = "Sắp xếp ID thông minh vào Google Sheet với thuật toán tối ưu"
ICON = "🛒"

# Tạo Blueprint
basket_bp = Blueprint('basket_arrangement', __name__,
                     template_folder='templates',
                     static_folder='static',
                     url_prefix='/basket_arrangement')

def setup(app):
    """Đăng ký blueprint với Flask app"""
    app.register_blueprint(basket_bp)

@basket_bp.route('/')
def index():
    """Trang chính của Basket Arrangement"""
    return render_template('programs/basket_arrangement.html')

@basket_bp.route('/api/load_sheet', methods=['POST'])
def load_sheet():
    """API để load Google Sheet"""
    try:
        data = request.get_json()
        sheet_url = data.get('sheet_url', '')

        # Temporary simplified response for testing
        return jsonify({
            'success': True,
            'data': {
                'sheet_id': 'test_sheet_id',
                'sheet_names': ['Basket', 'Deal list'],
                'total_sheets': 2
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

@basket_bp.route('/api/get_time_slots', methods=['POST'])
def get_time_slots():
    """API để lấy danh sách khung giờ từ sheet"""
    try:
        # Temporary simplified response for testing
        return jsonify({
            'success': True,
            'time_slots': ['14:00-15:00', '15:00-16:00', '16:00-17:00', '17:00-18:00']
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@basket_bp.route('/api/process_arrangement', methods=['POST'])
def process_arrangement():
    """API để xử lý sắp xếp ID"""
    try:
        # Temporary simplified response for testing
        return jsonify({
            'success': True,
            'result': {
                'message': 'Đã xử lý sắp xếp thành công (test mode)',
                'results': {
                    '14:00-15:00': {'original_count': 10, 'new_count': 15, 'added_count': 5},
                    '15:00-16:00': {'original_count': 8, 'new_count': 12, 'added_count': 4}
                },
                'logs': ['Test log 1', 'Test log 2']
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }), 500

@basket_bp.route('/api/get_column_mapping', methods=['GET'])
def get_column_mapping():
    """API để lấy thông tin mapping cột"""
    try:
        # Temporary simplified response for testing
        return jsonify({
            'success': True,
            'mapping': {
                'shop_id': 'A',
                'item_id': 'B',
                'gmv': 'C',
                'review': 'D',
                'no': 'E'
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@basket_bp.route('/api/update_column_mapping', methods=['POST'])
def update_column_mapping():
    """API để cập nhật mapping cột"""
    try:
        data = request.get_json()
        mapping = data.get('mapping', {})

        # Lưu mapping vào session
        if 'basket_logic' not in session:
            session['basket_logic'] = {}
        session['basket_logic']['column_mapping'] = mapping
        session.modified = True

        return jsonify({
            'success': True,
            'message': 'Đã cập nhật mapping cột thành công'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
