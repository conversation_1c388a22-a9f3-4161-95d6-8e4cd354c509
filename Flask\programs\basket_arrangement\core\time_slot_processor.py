"""
TimeSlotProcessor - Copy đầy đủ từ basket_package.py
Đ<PERSON>m bảo 100% tính toàn vẹn logic xử lý từng khung giờ
"""

import random
import time
from .constants import (
    MAX_UNIQUE_IDS, TIME_SLOT_CONFIG, MESSAGE_TEMPLATES,
    MIN_DISTANCE_BETWEEN_INPUT_IDS, normalize_timeline
)
from .smart_placement import SmartPlacementEngine

class TimeSlotProcessor:
    """
    Xử lý logic cho từng khung giờ - Copy đầy đủ từ basket_package.py
    """
    
    def __init__(self, time_slot, column, deal_list_manager, exclusive_ids=None):
        self.time_slot = time_slot
        self.column = column
        self.deal_list_manager = deal_list_manager
        self.current_ids = []
        self.protected_ids = set()
        self.fixed_positions = {}
        self.exclusive_ids = exclusive_ids or []  # Các ID có chế độ Exclusive - luôn giữ nguyên vị trí
        self.exclusive_positions = {}  # L<PERSON><PERSON> vị trí hiện tại của các ID exclusive {id: position}
        self.conditions = []  # List các điều kiện áp dụng cho khung giờ này
        # Khởi tạo seed ngẫu nhiên riêng cho từng khung giờ để đảm bảo sự khác biệt
        self.random_seed = random.randint(1, 10000)
        
        # Khởi tạo Smart Placement Engine
        self.smart_engine = SmartPlacementEngine(deal_list_manager)

    def set_log_function(self, log_function):
        """Thiết lập hàm log"""
        self.log = log_function
        self.smart_engine.log = log_function

    def add_condition(self, top_limit, input_ids, is_grouped=False):
        """Thêm điều kiện xử lý cho khung giờ này"""
        condition = {
            'top_limit': top_limit,
            'input_ids': input_ids.copy(),
            'is_grouped': is_grouped
        }
        self.conditions.append(condition)

    def clear_conditions(self):
        """Xóa tất cả điều kiện"""
        self.conditions.clear()

    def process_all_conditions(self):
        """Xử lý tất cả điều kiện đã được thêm vào"""
        if not self.conditions:
            self.log(f"Không có điều kiện nào cho khung giờ {self.time_slot}")
            return

        self.log(f"\n=== XỬ LÝ KHUNG GIỜ: {self.time_slot} ===")
        self.log(f"Số điều kiện: {len(self.conditions)}")
        
        # Lưu vị trí của các ID exclusive trước khi xử lý
        self._save_exclusive_positions()
        
        # Xử lý từng điều kiện
        for i, condition in enumerate(self.conditions):
            self.log(f"\n--- Điều kiện {i+1}/{len(self.conditions)} ---")
            self._process_single_condition(condition)
        
        # Đảm bảo các ID exclusive vẫn ở vị trí cũ
        self._restore_exclusive_positions()
        
        # Giới hạn số lượng ID theo MAX_UNIQUE_IDS
        if len(self.current_ids) > MAX_UNIQUE_IDS:
            self.current_ids = self.current_ids[:MAX_UNIQUE_IDS]
            self.log(f"Giới hạn danh sách xuống {MAX_UNIQUE_IDS} ID")

    def _save_exclusive_positions(self):
        """Lưu vị trí hiện tại của các ID exclusive"""
        self.exclusive_positions = {}
        for i, id_val in enumerate(self.current_ids):
            if id_val in self.exclusive_ids:
                self.exclusive_positions[id_val] = i
                self.log(f"Lưu vị trí exclusive: {id_val} tại vị trí {i}")

    def _restore_exclusive_positions(self):
        """Khôi phục vị trí của các ID exclusive"""
        for id_val, original_pos in self.exclusive_positions.items():
            if id_val in self.current_ids:
                current_pos = self.current_ids.index(id_val)
                if current_pos != original_pos:
                    # Di chuyển ID về vị trí cũ
                    self.current_ids.pop(current_pos)
                    if original_pos < len(self.current_ids):
                        self.current_ids.insert(original_pos, id_val)
                    else:
                        # Mở rộng danh sách nếu cần
                        while len(self.current_ids) <= original_pos:
                            self.current_ids.append("")
                        self.current_ids[original_pos] = id_val
                    self.log(f"Khôi phục vị trí exclusive: {id_val} từ {current_pos} về {original_pos}")

    def _process_single_condition(self, condition):
        """Xử lý một điều kiện cụ thể"""
        top_limit = condition['top_limit']
        input_ids = condition['input_ids']
        is_grouped = condition['is_grouped']
        
        self.log(f"Top limit: {top_limit}")
        self.log(f"Số ID input: {len(input_ids)}")
        self.log(f"Nhóm: {is_grouped}")
        
        # Lọc ID input thuộc timeline này
        timeline_input_ids = self._filter_ids_by_timeline(input_ids)
        self.log(f"ID thuộc timeline {self.time_slot}: {len(timeline_input_ids)}")
        
        if not timeline_input_ids:
            self.log("Không có ID nào thuộc timeline này")
            return
        
        # Xử lý theo chế độ nhóm hoặc không nhóm
        if is_grouped:
            self._process_grouped_ids(timeline_input_ids, top_limit)
        else:
            self._process_non_grouped_ids(timeline_input_ids, top_limit)

    def _filter_ids_by_timeline(self, input_ids):
        """Lọc ID thuộc timeline hiện tại"""
        timeline_ids = []
        normalized_timeline = normalize_timeline(self.time_slot)
        
        for id_val in input_ids:
            if id_val.strip():
                id_timeline = self.deal_list_manager.get_normalized_timeline(id_val)
                if id_timeline == normalized_timeline:
                    timeline_ids.append(id_val)
        
        return timeline_ids

    def _process_grouped_ids(self, input_ids, top_limit):
        """Xử lý ID nhóm (sử dụng thuật toán cũ)"""
        self.log(f"Xử lý {len(input_ids)} ID nhóm với thuật toán cũ")
        
        # Sử dụng seed riêng cho khung giờ này
        random.seed(self.random_seed + hash(self.time_slot) % 1000)
        
        for id_val in input_ids:
            if id_val in self.exclusive_ids:
                self.log(f"Bỏ qua ID exclusive: {id_val}")
                continue
                
            # Tìm vị trí ngẫu nhiên trong phạm vi top_limit
            avoid_positions = self.get_avoid_positions(top_limit)
            max_pos = min(top_limit - 1, len(self.current_ids))
            
            if avoid_positions < max_pos:
                position = random.randint(avoid_positions, max_pos)
            else:
                position = max_pos
            
            # Chèn ID vào vị trí
            self._insert_id_at_position(id_val, position)
            self.log(f"Chèn ID nhóm {id_val} vào vị trí {position}")

    def _process_non_grouped_ids(self, input_ids, top_limit):
        """Xử lý ID không nhóm với Smart Placement Engine"""
        self.log(f"Xử lý {len(input_ids)} ID không nhóm với Smart Placement")
        
        # Tạo dictionary cho Smart Placement Engine
        top_limits = {id_val: top_limit for id_val in input_ids}
        is_grouped_flags = {id_val: False for id_val in input_ids}
        
        # Sử dụng Smart Placement Engine
        placements = self.smart_engine.find_optimal_positions(
            self.current_ids, input_ids, top_limits, is_grouped_flags
        )
        
        # Áp dụng placements
        for id_val, position, placement_type in placements:
            if id_val in self.exclusive_ids:
                self.log(f"Bỏ qua ID exclusive: {id_val}")
                continue
                
            self._insert_id_at_position(id_val, position)
            self.log(f"Chèn {id_val} vào vị trí {position} ({placement_type})")

    def _insert_id_at_position(self, id_val, position):
        """Chèn ID vào vị trí cụ thể"""
        # Mở rộng danh sách nếu cần
        while len(self.current_ids) <= position:
            self.current_ids.append("")
        
        # Chèn ID
        if position >= len(self.current_ids):
            self.current_ids.append(id_val)
        else:
            self.current_ids.insert(position, id_val)

    def get_avoid_positions(self, top_limit):
        """Tính avoid positions dựa trên top limit"""
        avoid_positions = TIME_SLOT_CONFIG['AVOID_POSITIONS'].get(
            top_limit, 
            top_limit // TIME_SLOT_CONFIG['DEFAULT_AVOID_RATIO']
        )
        return avoid_positions

    def remove_excess_ids(self, timeline_to_remove):
        """Xóa ID dư thừa không thuộc timeline hiện tại"""
        if not timeline_to_remove:
            return
        
        normalized_timeline = normalize_timeline(timeline_to_remove)
        removed_count = 0
        
        # Tạo danh sách mới không chứa ID của timeline cần xóa
        new_ids = []
        for id_val in self.current_ids:
            if not id_val or not id_val.strip():
                new_ids.append(id_val)
                continue
                
            id_timeline = self.deal_list_manager.get_normalized_timeline(id_val)
            if id_timeline != normalized_timeline:
                new_ids.append(id_val)
            else:
                # Kiểm tra xem có phải ID exclusive không
                if id_val in self.exclusive_ids:
                    new_ids.append(id_val)  # Giữ lại ID exclusive
                    self.log(f"Giữ lại ID exclusive: {id_val}")
                else:
                    removed_count += 1
                    self.log(f"Xóa ID: {id_val} (timeline: {timeline_to_remove})")
        
        self.current_ids = new_ids
        self.log(f"Đã xóa {removed_count} ID thuộc timeline {timeline_to_remove}")

    def sort_by_no_and_review(self):
        """Sắp xếp ID theo NO và Review (thuật toán v2.0)"""
        if not self.current_ids:
            return
        
        self.log(f"Sắp xếp {len(self.current_ids)} ID theo NO và Review")
        
        # Tạo danh sách với thông tin để sắp xếp
        id_info_list = []
        for i, id_val in enumerate(self.current_ids):
            if not id_val or not id_val.strip():
                id_info_list.append({
                    'id': id_val,
                    'no': 999999,
                    'has_review': False,
                    'gmv': 0.0,
                    'original_index': i,
                    'is_exclusive': False
                })
                continue
            
            no = self.deal_list_manager.get_no(id_val)
            has_review = self.deal_list_manager.has_review(id_val)
            gmv = self.deal_list_manager.get_gmv(id_val)
            is_exclusive = id_val in self.exclusive_ids
            
            id_info_list.append({
                'id': id_val,
                'no': no,
                'has_review': has_review,
                'gmv': gmv,
                'original_index': i,
                'is_exclusive': is_exclusive
            })
        
        # Sắp xếp theo thứ tự ưu tiên:
        # 1. ID exclusive giữ nguyên vị trí (không sắp xếp)
        # 2. NO tăng dần (thứ tự NO là bất khả xâm phạm)
        # 3. Review giảm dần (có review trước, không review sau)
        # 4. GMV giảm dần
        sorted_list = sorted(id_info_list, key=lambda x: (
            x['is_exclusive'],     # ID exclusive lên đầu để giữ nguyên vị trí
            x['no'],               # NO tăng dần
            not x['has_review'],   # Review: True trước False
            -x['gmv']              # GMV giảm dần
        ))
        
        # Tạo danh sách mới
        new_ids = []
        exclusive_handled = set()
        
        for item in sorted_list:
            if item['is_exclusive'] and item['id'] not in exclusive_handled:
                # Giữ nguyên vị trí của ID exclusive
                original_pos = item['original_index']
                while len(new_ids) <= original_pos:
                    new_ids.append("")
                new_ids[original_pos] = item['id']
                exclusive_handled.add(item['id'])
            elif not item['is_exclusive']:
                # Thêm ID không exclusive vào cuối
                new_ids.append(item['id'])
        
        self.current_ids = new_ids
        self.log(f"✅ Đã sắp xếp {len(self.current_ids)} ID theo NO và Review")

    def get_final_data(self):
        """Lấy dữ liệu cuối cùng để ghi vào sheet"""
        return self.current_ids.copy()

    def get_statistics(self):
        """Lấy thống kê về khung giờ này"""
        total_ids = len([id_val for id_val in self.current_ids if id_val and id_val.strip()])
        review_ids = len([id_val for id_val in self.current_ids 
                         if id_val and id_val.strip() and self.deal_list_manager.has_review(id_val)])
        exclusive_ids = len([id_val for id_val in self.current_ids if id_val in self.exclusive_ids])
        
        return {
            'time_slot': self.time_slot,
            'column': self.column,
            'total_ids': total_ids,
            'review_ids': review_ids,
            'exclusive_ids': exclusive_ids,
            'conditions_count': len(self.conditions)
        }
